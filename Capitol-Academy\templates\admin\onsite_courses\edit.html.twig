{% extends 'admin/base.html.twig' %}

{% block title %}Edit Onsite Course - Capitol Academy Admin{% endblock %}

{% block page_title %}Edit Onsite Course{% endblock %}

{% block breadcrumbs %}
<li class="breadcrumb-item"><a href="{{ path('admin_dashboard') }}">Home</a></li>
<li class="breadcrumb-item"><a href="{{ path('admin_onsite_courses') }}">Onsite Courses</a></li>
<li class="breadcrumb-item active">Edit {{ course.title }}</li>
{% endblock %}

{% block content %}
<div class="container-fluid">


    <!-- Integrated Header with Content -->
    <div class="card border-0 shadow-lg mb-4">
        <div class="card-header" style="background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%); color: white; padding: 1.5rem; border-radius: 15px 15px 0 0;">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h2 class="card-title mb-0" style="font-size: 1.8rem; font-weight: 600;">
                        <i class="fas fa-edit mr-3" style="font-size: 2rem;"></i>
                        Edit Onsite Course
                    </h2>
                </div>
                <div class="col-md-6">
                    <div class="d-flex justify-content-end align-items-center flex-wrap">
                        <!-- Back to Onsite Courses Button -->
                        <a href="{{ path('admin_onsite_courses') }}"
                           class="btn mb-2 mb-md-0 me-2"
                           style="font-weight: 600; border-radius: 8px; padding: 0.75rem 1.5rem; background: white; color: #011a2d; border: 2px solid #011a2d; transition: all 0.3s ease;"
                           onmouseover="this.style.background='#011a2d'; this.style.color='white';"
                           onmouseout="this.style.background='white'; this.style.color='#011a2d';">
                            <i class="fas fa-arrow-left me-2"></i>
                            Back to Onsite Courses
                        </a>
                        <!-- Preview Button -->
                        <a href="{{ path('admin_onsite_course_preview', {'code': course.code}) }}"
                           class="btn mb-2 mb-md-0"
                           style="font-weight: 600; border-radius: 8px; padding: 0.75rem 1.5rem; background: #17a2b8; color: white; border: 2px solid #17a2b8; transition: all 0.3s ease;"
                           onmouseover="this.style.background='#138496';"
                           onmouseout="this.style.background='#17a2b8';">
                            <i class="fas fa-eye me-2"></i>
                            Preview
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <form method="post" class="needs-validation" enctype="multipart/form-data" novalidate>
            <input type="hidden" name="_token" value="{{ csrf_token('onsite_course_edit') }}">
            <input type="hidden" name="is_active" value="{{ course.isActive ? '1' : '0' }}">
            <div class="card-body">
                <!-- Single Column Layout -->
                <div class="row">
                    <div class="col-12">
                        <!-- Course Code and Title Row -->
                        <div class="row">
                            <!-- Course Code -->
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="code" class="form-label">
                                        <i class="fas fa-hashtag text-primary mr-1" aria-hidden="true"></i>
                                        Course Code <span class="text-danger" aria-label="required">*</span>
                                    </label>
                                    <input type="text"
                                           class="form-control enhanced-field"
                                           id="code"
                                           name="code"
                                           value="{{ course.code }}"
                                           placeholder="e.g., OSC001, TRAD101"
                                           required
                                           maxlength="10"
                                           style="height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;">
                                    <div class="invalid-feedback">
                                        Please provide a course code.
                                    </div>
                                </div>
                            </div>

                            <!-- Course Title -->
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="title" class="form-label">
                                        <i class="fas fa-graduation-cap text-primary mr-1" aria-hidden="true"></i>
                                        Course Title <span class="text-danger" aria-label="required">*</span>
                                    </label>
                                    <input type="text"
                                           class="form-control enhanced-field"
                                           id="title"
                                           name="title"
                                           value="{{ course.title }}"
                                           placeholder="Enter course title"
                                           required
                                           maxlength="255"
                                           style="height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;">
                                    <div class="invalid-feedback">
                                        Please provide a course title.
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Category and Level Row -->
                        <div class="row">
                            <!-- Category -->
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="category" class="form-label">
                                        <i class="fas fa-folder text-primary mr-1" aria-hidden="true"></i>
                                        Category
                                    </label>
                                    <select class="form-select enhanced-dropdown"
                                            id="category"
                                            name="category"
                                            aria-describedby="category_help category_error"
                                            aria-label="Select a course category"
                                            style="height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; background-color: #fff;">
                                        <option value="">Choose a category...</option>
                                        {% for category in categories %}
                                            <option value="{{ category.name }}" {{ course.category == category.name ? 'selected' : '' }}>{{ category.name }}</option>
                                        {% endfor %}
                                    </select>
                                    <div id="category_help" class="form-text text-muted" style="display: none;">
                                        Select the category that best describes this course. Use arrow keys to navigate options.
                                    </div>
                                    <div id="category_error" class="invalid-feedback" role="alert" aria-live="polite">
                                        Please select a category.
                                    </div>
                                </div>
                            </div>

                            <!-- Level -->
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="level" class="form-label">
                                        <i class="fas fa-layer-group text-primary mr-1" aria-hidden="true"></i>
                                        Level
                                    </label>
                                    <select class="form-select enhanced-dropdown"
                                            id="level"
                                            name="level"
                                            aria-describedby="level_help level_error"
                                            aria-label="Select course level"
                                            style="height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; background-color: #fff;">
                                        <option value="">Choose a level...</option>
                                        <option value="Beginner" {{ course.level == 'Beginner' ? 'selected' : '' }}>Beginner</option>
                                        <option value="Intermediate" {{ course.level == 'Intermediate' ? 'selected' : '' }}>Intermediate</option>
                                        <option value="Advanced" {{ course.level == 'Advanced' ? 'selected' : '' }}>Advanced</option>
                                        <option value="Expert" {{ course.level == 'Expert' ? 'selected' : '' }}>Expert</option>
                                    </select>
                                    <div id="level_help" class="form-text text-muted" style="display: none;">
                                        Select the difficulty level of this course.
                                    </div>
                                    <div id="level_error" class="invalid-feedback" role="alert" aria-live="polite">
                                        Please select a level.
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Description -->
                        <div class="form-group">
                            <label for="description" class="form-label">
                                <i class="fas fa-align-left text-primary mr-1" aria-hidden="true"></i>
                                Course Description <span class="text-danger" aria-label="required">*</span>
                            </label>
                            <textarea class="form-control enhanced-field"
                                      id="description"
                                      name="description"
                                      rows="4"
                                      placeholder="Provide a detailed description of the course content, objectives, and what students will learn..."
                                      required
                                      style="font-size: 1rem; border: 2px solid #ced4da; resize: vertical;">{{ course.description }}</textarea>
                            <div class="invalid-feedback">
                                Please provide a course description.
                            </div>
                        </div>

                        <!-- Learning Outcomes -->
                        <div class="form-group">
                            <label class="form-label">
                                <i class="fas fa-bullseye text-primary mr-1" aria-hidden="true"></i>
                                Learning Outcomes <span class="text-danger" aria-label="required">*</span>
                            </label>
                            <div id="learning-outcomes-container">
                                {% if course.learningOutcomes %}
                                    {% for outcome in course.learningOutcomes %}
                                        <div class="input-group mb-2 learning-outcome-item">
                                            <input type="text"
                                                   class="form-control enhanced-field"
                                                   name="learning_outcomes[]"
                                                   value="{{ outcome }}"
                                                   placeholder="Enter a learning outcome..."
                                                   required
                                                   style="height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; border-radius: 0.375rem 0 0 0.375rem;">
                                            <div class="input-group-append">
                                                {% if loop.first %}
                                                    <button type="button" class="btn btn-success add-learning-outcome" style="height: calc(1.6em + 1.25rem + 4px); border-radius: 0 0.375rem 0.375rem 0;">
                                                        <i class="fas fa-plus"></i>
                                                    </button>
                                                {% else %}
                                                    <button type="button" class="btn btn-danger remove-learning-outcome" style="height: calc(1.6em + 1.25rem + 4px); border-radius: 0;">
                                                        <i class="fas fa-minus"></i>
                                                    </button>
                                                    <button type="button" class="btn btn-success add-learning-outcome" style="height: calc(1.6em + 1.25rem + 4px); border-radius: 0 0.375rem 0.375rem 0;">
                                                        <i class="fas fa-plus"></i>
                                                    </button>
                                                {% endif %}
                                            </div>
                                        </div>
                                    {% endfor %}
                                {% else %}
                                    <div class="input-group mb-2 learning-outcome-item">
                                        <input type="text"
                                               class="form-control enhanced-field"
                                               name="learning_outcomes[]"
                                               placeholder="e.g., Master advanced chart analysis techniques"
                                               required
                                               style="height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; border-radius: 0.375rem 0 0 0.375rem;">
                                        <div class="input-group-append">
                                            <button type="button" class="btn btn-success add-learning-outcome" style="height: calc(1.6em + 1.25rem + 4px); border-radius: 0 0.375rem 0.375rem 0;">
                                                <i class="fas fa-plus"></i>
                                            </button>
                                        </div>
                                    </div>
                                {% endif %}
                            </div>
                            <small class="form-text text-muted">Add specific learning outcomes for this course. Click + to add more.</small>
                        </div>

                        <!-- Features -->
                        <div class="form-group">
                            <label class="form-label">
                                <i class="fas fa-star text-primary mr-1" aria-hidden="true"></i>
                                Course Features <span class="text-danger" aria-label="required">*</span>
                            </label>
                            <div id="features-container">
                                {% if course.features %}
                                    {% for feature in course.features %}
                                        <div class="input-group mb-2 feature-item">
                                            <input type="text"
                                                   class="form-control enhanced-field"
                                                   name="features[]"
                                                   value="{{ feature }}"
                                                   placeholder="Enter a feature..."
                                                   required
                                                   style="height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; border-radius: 0.375rem 0 0 0.375rem;">
                                            <div class="input-group-append">
                                                {% if loop.first %}
                                                    <button type="button" class="btn btn-success add-feature" style="height: calc(1.6em + 1.25rem + 4px); border-radius: 0 0.375rem 0.375rem 0;">
                                                        <i class="fas fa-plus"></i>
                                                    </button>
                                                {% else %}
                                                    <button type="button" class="btn btn-danger remove-feature" style="height: calc(1.6em + 1.25rem + 4px); border-radius: 0;">
                                                        <i class="fas fa-minus"></i>
                                                    </button>
                                                    <button type="button" class="btn btn-success add-feature" style="height: calc(1.6em + 1.25rem + 4px); border-radius: 0 0.375rem 0.375rem 0;">
                                                        <i class="fas fa-plus"></i>
                                                    </button>
                                                {% endif %}
                                            </div>
                                        </div>
                                    {% endfor %}
                                {% else %}
                                    <div class="input-group mb-2 feature-item">
                                        <input type="text"
                                               class="form-control enhanced-field"
                                               name="features[]"
                                               placeholder="e.g., Live instructor sessions, Hands-on exercises, Certificate of completion"
                                               required
                                               style="height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; border-radius: 0.375rem 0 0 0.375rem;">
                                        <div class="input-group-append">
                                            <button type="button" class="btn btn-success add-feature" style="height: calc(1.6em + 1.25rem + 4px); border-radius: 0 0.375rem 0.375rem 0;">
                                                <i class="fas fa-plus"></i>
                                            </button>
                                        </div>
                                    </div>
                                {% endif %}
                            </div>
                            <small class="form-text text-muted">Add key features and benefits of this course. Click + to add more.</small>
                        </div>

                        <!-- Thumbnail Upload -->
                        <div class="form-group">
                            <label for="thumbnail_image" class="form-label">
                                <i class="fas fa-image text-primary mr-1" aria-hidden="true"></i>
                                Course Thumbnail
                            </label>
                            <input type="file"
                                   class="form-control enhanced-file-field"
                                   id="thumbnail_image"
                                   name="thumbnail_image"
                                   accept="image/*"
                                   style="height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;">
                            <small class="form-text text-muted help-text" style="display: none;">
                                Upload a thumbnail image (JPEG, PNG, WebP). Recommended size: 300x200px. Leave empty to keep current thumbnail.
                            </small>

                            <!-- Current Thumbnail Display -->
                            {% if course.thumbnailImage %}
                            <div class="current-thumbnail mt-3 d-flex flex-column align-items-center">
                                <div class="professional-image-container" style="width: 600px; height: 300px; border: 2px solid #1e3c72; border-radius: 8px; overflow: hidden; background: #f8f9fa;">
                                    <img src="{{ course.thumbnailUrl }}" alt="Current Thumbnail" class="w-100 h-100" style="object-fit: cover;">
                                </div>
                                <small class="form-text text-info d-block mt-2 text-center">Current Thumbnail (300x200px)</small>
                            </div>
                            {% endif %}
                        </div>

                        <!-- Course has modules toggle -->
                        <div class="form-group">
                            <label class="form-label">
                                <i class="fas fa-puzzle-piece text-primary mr-1" aria-hidden="true"></i>
                                Course Structure
                            </label>
                            <div class="form-check form-switch" style="padding-left: 0; margin-top: 0.5rem;">
                                <input class="form-check-input" type="checkbox" id="has_modules" name="has_modules" value="1" {{ course.hasModules ? 'checked' : '' }} style="margin-left: 0;">
                                <label class="form-check-label" for="has_modules" style="margin-left: 2.5rem;">
                                    Course has modules
                                </label>
                            </div>
                            <small class="form-text text-muted">Enable this if you want to add modules to this course.</small>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card-footer" style="background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); border-top: 1px solid #dee2e6; padding: 1.5rem;">
                <div class="row">
                    <div class="col-md-6">
                        <button type="submit" class="btn btn-lg" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; border: none; font-weight: 600; border-radius: 8px; padding: 0.75rem 2rem; transition: all 0.3s ease;">
                            <i class="fas fa-save mr-2"></i>
                            Update Onsite Course
                        </button>
                    </div>
                    <div class="col-md-6 text-right">
                        <a href="{{ path('admin_onsite_courses') }}" class="btn btn-secondary btn-lg" style="font-weight: 600; border-radius: 8px; padding: 0.75rem 2rem;">
                            <i class="fas fa-times mr-2"></i>
                            Cancel
                        </a>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block javascripts %}
<script>
$(document).ready(function() {
    // Dynamic Learning Outcomes Management
    $(document).on('click', '.add-learning-outcome', function() {
        var container = $('#learning-outcomes-container');
        var newItem = `
            <div class="input-group mb-2 learning-outcome-item">
                <input type="text"
                       class="form-control enhanced-field"
                       name="learning_outcomes[]"
                       placeholder="Enter a learning outcome..."
                       required
                       style="height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; border-radius: 0.375rem 0 0 0.375rem;">
                <div class="input-group-append">
                    <button type="button" class="btn btn-danger remove-learning-outcome" style="height: calc(1.6em + 1.25rem + 4px); border-radius: 0;">
                        <i class="fas fa-minus"></i>
                    </button>
                    <button type="button" class="btn btn-success add-learning-outcome" style="height: calc(1.6em + 1.25rem + 4px); border-radius: 0 0.375rem 0.375rem 0;">
                        <i class="fas fa-plus"></i>
                    </button>
                </div>
            </div>
        `;
        container.append(newItem);
    });

    // Remove learning outcome
    $(document).on('click', '.remove-learning-outcome', function() {
        $(this).closest('.learning-outcome-item').remove();
    });

    // Dynamic Features Management
    $(document).on('click', '.add-feature', function() {
        var container = $('#features-container');
        var newItem = `
            <div class="input-group mb-2 feature-item">
                <input type="text"
                       class="form-control enhanced-field"
                       name="features[]"
                       placeholder="e.g., Live instructor sessions, Downloadable resources..."
                       required
                       style="height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; border-radius: 0.375rem 0 0 0.375rem;">
                <div class="input-group-append">
                    <button type="button" class="btn btn-danger remove-feature" style="height: calc(1.6em + 1.25rem + 4px); border-radius: 0;">
                        <i class="fas fa-minus"></i>
                    </button>
                    <button type="button" class="btn btn-success add-feature" style="height: calc(1.6em + 1.25rem + 4px); border-radius: 0 0.375rem 0.375rem 0;">
                        <i class="fas fa-plus"></i>
                    </button>
                </div>
            </div>
        `;
        container.append(newItem);
    });

    // Remove feature
    $(document).on('click', '.remove-feature', function() {
        $(this).closest('.feature-item').remove();
    });

    // Thumbnail image preview
    const thumbnailInput = document.getElementById('thumbnail_image');
    if (thumbnailInput) {
        thumbnailInput.addEventListener('change', function(e) {
            const file = e.target.files[0];
            const previewContainer = document.getElementById('thumbnail-preview');
            const previewImg = document.getElementById('thumbnail-preview-img');

            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    previewImg.src = e.target.result;
                    previewContainer.style.display = 'flex';
                };
                reader.readAsDataURL(file);
            } else {
                previewContainer.style.display = 'none';
            }
        });
    }

    // Bootstrap form validation
    (function() {
        'use strict';
        window.addEventListener('load', function() {
            var forms = document.getElementsByClassName('needs-validation');
            var validation = Array.prototype.filter.call(forms, function(form) {
                form.addEventListener('submit', function(event) {
                    console.log('Form submission attempted');

                    var submitBtn = form.querySelector('button[type="submit"]');

                    // Check if form is valid using native HTML5 validation
                    if (form.checkValidity() === false) {
                        console.log('Form validation failed');
                        event.preventDefault();
                        event.stopPropagation();

                        // Reset button state on validation failure
                        if (submitBtn) {
                            submitBtn.disabled = false;
                            submitBtn.innerHTML = '<i class="fas fa-save mr-2"></i>Update Onsite Course';
                        }

                        // Show help text when validation fails
                        $('.help-text').show();
                    } else {
                        console.log('Form validation passed, submitting...');
                        // Show loading state on successful validation
                        if (submitBtn) {
                            submitBtn.disabled = true;
                            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Updating Course...';
                        }

                        // Hide help text when form is valid
                        $('.help-text').hide();
                    }

                    // Add validation classes for styling
                    form.classList.add('was-validated');
                }, false);
            });
        }, false);
    })();

    // Toggle module section visibility (if modules section exists)
    $('#has_modules').on('change', function() {
        const modulesSection = $('#modules-section');
        if (modulesSection.length) {
            if ($(this).is(':checked')) {
                modulesSection.slideDown();
            } else {
                modulesSection.slideUp();
            }
        }
    });

    // Thumbnail preview functionality
    const thumbnailInput = document.getElementById('thumbnail_image');
    if (thumbnailInput) {
        thumbnailInput.addEventListener('change', function(e) {
            const file = e.target.files[0];
            const currentThumbnailContainer = document.querySelector('.current-thumbnail');

            if (file && file.type.startsWith('image/')) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    // Update the current thumbnail display with new file
                    if (currentThumbnailContainer) {
                        const imgElement = currentThumbnailContainer.querySelector('img');
                        if (imgElement) {
                            imgElement.src = e.target.result;
                        }

                        // Update the label to show it's a new thumbnail
                        const label = currentThumbnailContainer.querySelector('small');
                        if (label) {
                            label.innerHTML = `New Thumbnail Selected: ${file.name}`;
                            label.className = 'form-text text-success d-block mt-2 text-center';
                        }
                    } else {
                        // Create new thumbnail preview if no current thumbnail exists
                        const thumbnailContainer = document.createElement('div');
                        thumbnailContainer.className = 'current-thumbnail mt-3 d-flex flex-column align-items-center';
                        thumbnailContainer.innerHTML = `
                            <div class="professional-image-container" style="width: 600px; height: 300px; border: 2px solid #1e3c72; border-radius: 8px; overflow: hidden; background: #f8f9fa;">
                                <img src="${e.target.result}" alt="Thumbnail Preview" class="w-100 h-100" style="object-fit: cover;">
                            </div>
                            <small class="form-text text-success d-block mt-2 text-center">New Thumbnail Selected: ${file.name}</small>
                        `;
                        thumbnailInput.parentNode.appendChild(thumbnailContainer);
                    }
                };
                reader.readAsDataURL(file);
            }
        });
    }

    // Enhanced category selection with search functionality
    const categorySelect = document.getElementById('category');
    if (categorySelect) {
        $(categorySelect).select2({
            placeholder: 'Search and select a category...',
            allowClear: true,
            width: '100%',
            theme: 'bootstrap4'
        });
    }

    // Enhanced level selection with search functionality
    const levelSelect = document.getElementById('level');
    if (levelSelect) {
        $(levelSelect).select2({
            placeholder: 'Search and select a level...',
            allowClear: true,
            width: '100%',
            theme: 'bootstrap4'
        });
    }
});
</script>

<!-- Include Select2 for enhanced dropdowns -->
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<link href="https://cdn.jsdelivr.net/npm/@ttskch/select2-bootstrap4-theme@x.x.x/dist/select2-bootstrap4.min.css" rel="stylesheet" />
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

<style>
/* Select2 Custom Styling */
.select2-container--bootstrap4 .select2-selection--single {
    height: calc(1.6em + 1.25rem + 4px) !important;
    border: 2px solid #ced4da !important;
    border-radius: 8px !important;
    font-size: 1rem !important;
}

.select2-container--bootstrap4 .select2-selection--single .select2-selection__rendered {
    padding-left: 1rem !important;
    padding-top: 0.75rem !important;
    line-height: 1.5 !important;
}

.select2-container--bootstrap4 .select2-selection--single .select2-selection__arrow {
    height: calc(1.6em + 1.25rem + 2px) !important;
    right: 1rem !important;
}

.enhanced-dropdown option:first-child {
    color: #6c757d;
    font-style: italic;
    font-weight: 400;
}

.enhanced-dropdown option:not(:first-child) {
    color: #011a2d;
    font-weight: 500;
}
</style>
{% endblock %}
