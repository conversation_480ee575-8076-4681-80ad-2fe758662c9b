<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\CoreExtension;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;
use Twig\TemplateWrapper;

/* admin/onsite_courses/preview.html.twig */
class __TwigTemplate_1d26feb97aa7245c285189a0c3404e5e extends Template
{
    private Source $source;
    /**
     * @var array<string, Template>
     */
    private array $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->blocks = [
            'title' => [$this, 'block_title'],
            'page_title' => [$this, 'block_page_title'],
            'breadcrumbs' => [$this, 'block_breadcrumbs'],
            'content' => [$this, 'block_content'],
        ];
    }

    protected function doGetParent(array $context): bool|string|Template|TemplateWrapper
    {
        // line 1
        return "admin/base.html.twig";
    }

    protected function doDisplay(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/onsite_courses/preview.html.twig"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/onsite_courses/preview.html.twig"));

        $this->parent = $this->load("admin/base.html.twig", 1);
        yield from $this->parent->unwrap()->yield($context, array_merge($this->blocks, $blocks));
        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

    }

    // line 3
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_title(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        yield "Onsite Course Details - Capitol Academy Admin";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 5
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_page_title(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "page_title"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "page_title"));

        yield "Onsite Course Details";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 7
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_breadcrumbs(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "breadcrumbs"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "breadcrumbs"));

        // line 8
        yield "<li class=\"breadcrumb-item\"><a href=\"";
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_dashboard");
        yield "\">Home</a></li>
<li class=\"breadcrumb-item\"><a href=\"";
        // line 9
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_onsite_courses");
        yield "\">Onsite Courses</a></li>
<li class=\"breadcrumb-item active\">";
        // line 10
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 10, $this->source); })()), "title", [], "any", false, false, false, 10), "html", null, true);
        yield "</li>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 13
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_content(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "content"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "content"));

        // line 14
        yield "<div class=\"container-fluid\">


    <!-- Integrated Header with Content -->
    <div class=\"card border-0 shadow-lg mb-4\">
        <div class=\"card-header\" style=\"background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%); color: white; padding: 1.5rem; border-radius: 15px 15px 0 0;\">
            <div class=\"row align-items-center\">
                <div class=\"col-md-6\">
                    <h2 class=\"card-title mb-0\" style=\"font-size: 1.8rem; font-weight: 600;\">
                        <i class=\"fas fa-graduation-cap mr-3\" style=\"font-size: 2rem;\"></i>
                        Onsite Course Details: ";
        // line 24
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 24, $this->source); })()), "title", [], "any", false, false, false, 24), "html", null, true);
        yield "
                    </h2>
                </div>
                <div class=\"col-md-6\">
                    <div class=\"d-flex justify-content-end align-items-center flex-wrap\">
                        <!-- Edit Onsite Course Button (Icon Only) -->
                        <a href=\"";
        // line 30
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_onsite_course_edit", ["code" => CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 30, $this->source); })()), "code", [], "any", false, false, false, 30)]), "html", null, true);
        yield "\"
                           class=\"btn me-2 mb-2 mb-md-0\"
                           style=\"border-radius: 50%; width: 45px; height: 45px; display: inline-flex; align-items: center; justify-content: center; background: white; border: 2px solid #011a2d; transition: all 0.3s ease;\"
                           onmouseover=\"this.style.background='#011a2d'; this.querySelector('i').style.color='white';\"
                           onmouseout=\"this.style.background='white'; this.querySelector('i').style.color='#011a2d';\"
                           title=\"Edit Onsite Course\">
                            <i class=\"fas fa-edit\" style=\"color: #011a2d;\"></i>
                        </a>

                        <!-- Print Onsite Course Button (Icon Only) -->
                        <a href=\"javascript:void(0)\" onclick=\"window.print()\"
                           class=\"btn me-2 mb-2 mb-md-0\"
                           style=\"border-radius: 50%; width: 45px; height: 45px; display: inline-flex; align-items: center; justify-content: center; background: white; border: 2px solid #011a2d; transition: all 0.3s ease;\"
                           onmouseover=\"this.style.background='#011a2d'; this.querySelector('i').style.color='white';\"
                           onmouseout=\"this.style.background='white'; this.querySelector('i').style.color='#011a2d';\"
                           title=\"Print Onsite Course Details\">
                            <i class=\"fas fa-print\" style=\"color: #011a2d;\"></i>
                        </a>

                        <!-- Toggle Status Button -->
                        <button type=\"button\"
                                class=\"btn me-2 mb-2 mb-md-0\"
                                style=\"font-weight: 600; border-radius: 8px; padding: 0.75rem 1.5rem; background: ";
        // line 52
        yield (((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 52, $this->source); })()), "isActive", [], "any", false, false, false, 52)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) ? ("#6c757d") : ("#28a745"));
        yield "; color: white; border: 2px solid ";
        yield (((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 52, $this->source); })()), "isActive", [], "any", false, false, false, 52)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) ? ("#6c757d") : ("#28a745"));
        yield "; transition: all 0.3s ease;\"
                                onclick=\"toggleOnsiteCourseStatus(";
        // line 53
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 53, $this->source); })()), "id", [], "any", false, false, false, 53), "html", null, true);
        yield ", '";
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 53, $this->source); })()), "title", [], "any", false, false, false, 53), "html", null, true);
        yield "', ";
        yield (((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 53, $this->source); })()), "isActive", [], "any", false, false, false, 53)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) ? ("true") : ("false"));
        yield ")\">
                            <i class=\"fas fa-";
        // line 54
        yield (((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 54, $this->source); })()), "isActive", [], "any", false, false, false, 54)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) ? ("pause") : ("play"));
        yield " me-2\"></i>
                            ";
        // line 55
        yield (((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 55, $this->source); })()), "isActive", [], "any", false, false, false, 55)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) ? ("Deactivate") : ("Activate"));
        yield "
                        </button>

                        <!-- Back to Onsite Courses Button -->
                        <a href=\"";
        // line 59
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_onsite_courses");
        yield "\"
                           class=\"btn mb-2 mb-md-0\"
                           style=\"font-weight: 600; border-radius: 8px; padding: 0.75rem 1.5rem; background: white; color: #011a2d; border: 2px solid #011a2d; transition: all 0.3s ease;\"
                           onmouseover=\"this.style.background='#011a2d'; this.style.color='white';\"
                           onmouseout=\"this.style.background='white'; this.style.color='#011a2d';\">
                            <i class=\"fas fa-arrow-left me-2\"></i>
                            Back to Onsite Courses
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <div class=\"card-body\">
            <div class=\"row\">
                <div class=\"col-12\">
                    <!-- Course Code and Title (same line) -->
                    <div class=\"row mb-4\">
                        <div class=\"col-md-4\">
                            <div class=\"form-group\">
                                <label class=\"form-label\">
                                    <i class=\"fas fa-hashtag text-primary mr-1\"></i>
                                    Course Code
                                </label>
                                <div class=\"enhanced-display-field\" style=\"background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d;\">
                                    ";
        // line 84
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 84, $this->source); })()), "code", [], "any", false, false, false, 84), "html", null, true);
        yield "
                                </div>
                            </div>
                        </div>
                        <div class=\"col-md-8\">
                            <div class=\"form-group\">
                                <label class=\"form-label\">
                                    <i class=\"fas fa-graduation-cap text-primary mr-1\"></i>
                                    Course Title
                                </label>
                                <div class=\"enhanced-display-field\" style=\"background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d;\">
                                    ";
        // line 95
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 95, $this->source); })()), "title", [], "any", false, false, false, 95), "html", null, true);
        yield "
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Description -->
                    <div class=\"form-group mb-4\">
                        <label class=\"form-label\">
                            <i class=\"fas fa-align-left text-primary mr-1\"></i>
                            Description
                        </label>
                        <div class=\"enhanced-display-field\" style=\"background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d; min-height: 80px;\">
                            ";
        // line 108
        yield (((CoreExtension::getAttribute($this->env, $this->source, ($context["course"] ?? null), "description", [], "any", true, true, false, 108) &&  !(null === CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 108, $this->source); })()), "description", [], "any", false, false, false, 108)))) ? ($this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 108, $this->source); })()), "description", [], "any", false, false, false, 108), "html", null, true)) : ("No description provided"));
        yield "
                        </div>
                    </div>

                    <!-- Category and Level (same line) -->
                    <div class=\"row mb-4\">
                        <div class=\"col-md-6\">
                            <div class=\"form-group\">
                                <label class=\"form-label\">
                                    <i class=\"fas fa-folder text-primary mr-1\"></i>
                                    Category
                                </label>
                                <div class=\"enhanced-display-field\" style=\"background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d;\">
                                    ";
        // line 121
        yield (((CoreExtension::getAttribute($this->env, $this->source, ($context["course"] ?? null), "category", [], "any", true, true, false, 121) &&  !(null === CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 121, $this->source); })()), "category", [], "any", false, false, false, 121)))) ? ($this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 121, $this->source); })()), "category", [], "any", false, false, false, 121), "html", null, true)) : ("Uncategorized"));
        yield "
                                </div>
                            </div>
                        </div>
                        <div class=\"col-md-6\">
                            <div class=\"form-group\">
                                <label class=\"form-label\">
                                    <i class=\"fas fa-layer-group text-primary mr-1\"></i>
                                    Level
                                </label>
                                <div class=\"enhanced-display-field\" style=\"background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d;\">
                                    ";
        // line 132
        yield (((CoreExtension::getAttribute($this->env, $this->source, ($context["course"] ?? null), "level", [], "any", true, true, false, 132) &&  !(null === CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 132, $this->source); })()), "level", [], "any", false, false, false, 132)))) ? ($this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 132, $this->source); })()), "level", [], "any", false, false, false, 132), "html", null, true)) : ("Not specified"));
        yield "
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Learning Outcomes -->
                    ";
        // line 139
        if ((CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 139, $this->source); })()), "learningOutcomes", [], "any", false, false, false, 139) && (Twig\Extension\CoreExtension::length($this->env->getCharset(), CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 139, $this->source); })()), "learningOutcomes", [], "any", false, false, false, 139)) > 0))) {
            // line 140
            yield "                    <div class=\"form-group mb-4\">
                        <label class=\"form-label\">
                            <i class=\"fas fa-bullseye text-primary mr-1\"></i>
                            Learning Outcomes
                        </label>
                        <div class=\"enhanced-display-field\" style=\"background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d;\">
                            <ul class=\"mb-0\" style=\"padding-left: 1.5rem;\">
                                ";
            // line 147
            $context['_parent'] = $context;
            $context['_seq'] = CoreExtension::ensureTraversable(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 147, $this->source); })()), "learningOutcomes", [], "any", false, false, false, 147));
            foreach ($context['_seq'] as $context["_key"] => $context["outcome"]) {
                // line 148
                yield "                                    <li class=\"mb-2\">
                                        <i class=\"fas fa-check-circle text-success me-2\"></i>
                                        ";
                // line 150
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($context["outcome"], "html", null, true);
                yield "
                                    </li>
                                ";
            }
            $_parent = $context['_parent'];
            unset($context['_seq'], $context['_key'], $context['outcome'], $context['_parent']);
            $context = array_intersect_key($context, $_parent) + $_parent;
            // line 153
            yield "                            </ul>
                        </div>
                    </div>
                    ";
        }
        // line 157
        yield "
                    <!-- Features -->
                    ";
        // line 159
        if ((CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 159, $this->source); })()), "features", [], "any", false, false, false, 159) && (Twig\Extension\CoreExtension::length($this->env->getCharset(), CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 159, $this->source); })()), "features", [], "any", false, false, false, 159)) > 0))) {
            // line 160
            yield "                    <div class=\"form-group mb-4\">
                        <label class=\"form-label\">
                            <i class=\"fas fa-star text-primary mr-1\"></i>
                            Features
                        </label>
                        <div class=\"enhanced-display-field\" style=\"background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d;\">
                            <ul class=\"mb-0\" style=\"padding-left: 1.5rem;\">
                                ";
            // line 167
            $context['_parent'] = $context;
            $context['_seq'] = CoreExtension::ensureTraversable(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 167, $this->source); })()), "features", [], "any", false, false, false, 167));
            foreach ($context['_seq'] as $context["_key"] => $context["feature"]) {
                // line 168
                yield "                                    <li class=\"mb-2\">
                                        <i class=\"fas fa-star text-warning me-2\"></i>
                                        ";
                // line 170
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($context["feature"], "html", null, true);
                yield "
                                    </li>
                                ";
            }
            $_parent = $context['_parent'];
            unset($context['_seq'], $context['_key'], $context['feature'], $context['_parent']);
            $context = array_intersect_key($context, $_parent) + $_parent;
            // line 173
            yield "                            </ul>
                        </div>
                    </div>
                    ";
        }
        // line 177
        yield "
                    <!-- Thumbnail -->
                    <div class=\"form-group mb-4\">
                        <label class=\"form-label\">
                            <i class=\"fas fa-image text-primary mr-1\"></i>
                            Course Thumbnail
                        </label>
                        <div class=\"d-flex justify-content-center\">
                            ";
        // line 185
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 185, $this->source); })()), "thumbnailImage", [], "any", false, false, false, 185)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 186
            yield "                                <img src=\"";
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 186, $this->source); })()), "thumbnailUrl", [], "any", false, false, false, 186), "html", null, true);
            yield "\"
                                     alt=\"Course Thumbnail\"
                                     class=\"img-fluid\"
                                     style=\"width: 400px; height: 225px; object-fit: cover; border: 2px solid #1e3c72; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.15);\">
                            ";
        } else {
            // line 191
            yield "                                <div class=\"d-flex align-items-center justify-content-center\"
                                     style=\"width: 400px; height: 225px; border: 2px solid #ced4da; border-radius: 8px; background: #f8f9fa;\">
                                    <div class=\"text-center text-muted\">
                                        <i class=\"fas fa-image\" style=\"font-size: 3rem; margin-bottom: 1rem;\"></i>
                                        <p>No thumbnail available</p>
                                    </div>
                                </div>
                            ";
        }
        // line 199
        yield "                        </div>
                    </div>

                    <!-- Modules Information -->
                    ";
        // line 203
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 203, $this->source); })()), "hasModules", [], "any", false, false, false, 203)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 204
            yield "                    <div class=\"form-group mb-4\">
                        <label class=\"form-label\">
                            <i class=\"fas fa-puzzle-piece text-primary mr-1\"></i>
                            Course Modules
                        </label>
                        <div class=\"enhanced-display-field\" style=\"background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d;\">
                            ";
            // line 210
            if ((CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 210, $this->source); })()), "modules", [], "any", false, false, false, 210) && (Twig\Extension\CoreExtension::length($this->env->getCharset(), CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 210, $this->source); })()), "modules", [], "any", false, false, false, 210)) > 0))) {
                // line 211
                yield "                                <div class=\"row\">
                                    ";
                // line 212
                $context['_parent'] = $context;
                $context['_seq'] = CoreExtension::ensureTraversable(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 212, $this->source); })()), "modules", [], "any", false, false, false, 212));
                foreach ($context['_seq'] as $context["_key"] => $context["module"]) {
                    // line 213
                    yield "                                    <div class=\"col-md-6 mb-3\">
                                        <div class=\"module-card\" style=\"background: white; border: 1px solid #e9ecef; border-radius: 8px; padding: 1rem;\">
                                            <h6 class=\"mb-2\" style=\"color: #011a2d; font-weight: 600;\">
                                                <i class=\"fas fa-cube me-2\"></i>";
                    // line 216
                    yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["module"], "title", [], "any", false, false, false, 216), "html", null, true);
                    yield "
                                            </h6>
                                            ";
                    // line 218
                    if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, $context["module"], "description", [], "any", false, false, false, 218)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
                        // line 219
                        yield "                                                <p class=\"mb-2 text-muted\" style=\"font-size: 0.9rem;\">";
                        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(Twig\Extension\CoreExtension::slice($this->env->getCharset(), CoreExtension::getAttribute($this->env, $this->source, $context["module"], "description", [], "any", false, false, false, 219), 0, 100), "html", null, true);
                        if ((Twig\Extension\CoreExtension::length($this->env->getCharset(), CoreExtension::getAttribute($this->env, $this->source, $context["module"], "description", [], "any", false, false, false, 219)) > 100)) {
                            yield "...";
                        }
                        yield "</p>
                                            ";
                    }
                    // line 221
                    yield "                                            ";
                    if ((CoreExtension::getAttribute($this->env, $this->source, $context["module"], "learningOutcomes", [], "any", false, false, false, 221) && (Twig\Extension\CoreExtension::length($this->env->getCharset(), CoreExtension::getAttribute($this->env, $this->source, $context["module"], "learningOutcomes", [], "any", false, false, false, 221)) > 0))) {
                        // line 222
                        yield "                                                <small class=\"text-success\">
                                                    <i class=\"fas fa-check-circle me-1\"></i>";
                        // line 223
                        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(Twig\Extension\CoreExtension::length($this->env->getCharset(), CoreExtension::getAttribute($this->env, $this->source, $context["module"], "learningOutcomes", [], "any", false, false, false, 223)), "html", null, true);
                        yield " learning outcome";
                        yield (((Twig\Extension\CoreExtension::length($this->env->getCharset(), CoreExtension::getAttribute($this->env, $this->source, $context["module"], "learningOutcomes", [], "any", false, false, false, 223)) > 1)) ? ("s") : (""));
                        yield "
                                                </small>
                                            ";
                    }
                    // line 226
                    yield "                                        </div>
                                    </div>
                                    ";
                }
                $_parent = $context['_parent'];
                unset($context['_seq'], $context['_key'], $context['module'], $context['_parent']);
                $context = array_intersect_key($context, $_parent) + $_parent;
                // line 229
                yield "                                </div>
                            ";
            } else {
                // line 231
                yield "                                <p class=\"mb-0 text-muted\">
                                    <i class=\"fas fa-info-circle me-2\"></i>This course has modules enabled but no modules have been created yet.
                                </p>
                            ";
            }
            // line 235
            yield "                        </div>
                    </div>
                    ";
        }
        // line 238
        yield "
                    <!-- Status and Creation Date -->
                    <div class=\"row mb-4\">
                        <div class=\"col-md-6\">
                            <div class=\"form-group\">
                                <label class=\"form-label\">
                                    <i class=\"fas fa-toggle-on text-primary mr-1\"></i>
                                    Status
                                </label>
                                <div class=\"enhanced-display-field\" style=\"background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d; min-height: 3.5rem; display: flex; align-items: center;\">
                                    ";
        // line 248
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 248, $this->source); })()), "isActive", [], "any", false, false, false, 248)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 249
            yield "                                        <span class=\"badge bg-success\" style=\"font-size: 0.9rem; padding: 0.5rem 1rem;\">
                                            <i class=\"fas fa-check-circle mr-1\"></i>Active
                                        </span>
                                    ";
        } else {
            // line 253
            yield "                                        <span class=\"badge bg-secondary\" style=\"font-size: 0.9rem; padding: 0.5rem 1rem;\">
                                            <i class=\"fas fa-pause-circle mr-1\"></i>Inactive
                                        </span>
                                    ";
        }
        // line 257
        yield "                                </div>
                            </div>
                        </div>
                        <div class=\"col-md-6\">
                            <div class=\"form-group\">
                                <label class=\"form-label\">
                                    <i class=\"fas fa-calendar-plus text-primary mr-1\"></i>
                                    Creation Date
                                </label>
                                <div class=\"enhanced-display-field\" style=\"background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d; min-height: 3.5rem; display: flex; align-items: center;\">
                                    ";
        // line 267
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Twig\Extension\CoreExtension']->formatDate(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 267, $this->source); })()), "createdAt", [], "any", false, false, false, 267), "F j, Y \\a\\t g:i A"), "html", null, true);
        yield "
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName(): string
    {
        return "admin/onsite_courses/preview.html.twig";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable(): bool
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo(): array
    {
        return array (  553 => 267,  541 => 257,  535 => 253,  529 => 249,  527 => 248,  515 => 238,  510 => 235,  504 => 231,  500 => 229,  492 => 226,  484 => 223,  481 => 222,  478 => 221,  469 => 219,  467 => 218,  462 => 216,  457 => 213,  453 => 212,  450 => 211,  448 => 210,  440 => 204,  438 => 203,  432 => 199,  422 => 191,  413 => 186,  411 => 185,  401 => 177,  395 => 173,  386 => 170,  382 => 168,  378 => 167,  369 => 160,  367 => 159,  363 => 157,  357 => 153,  348 => 150,  344 => 148,  340 => 147,  331 => 140,  329 => 139,  319 => 132,  305 => 121,  289 => 108,  273 => 95,  259 => 84,  231 => 59,  224 => 55,  220 => 54,  212 => 53,  206 => 52,  181 => 30,  172 => 24,  160 => 14,  147 => 13,  134 => 10,  130 => 9,  125 => 8,  112 => 7,  89 => 5,  66 => 3,  43 => 1,);
    }

    public function getSourceContext(): Source
    {
        return new Source("{% extends 'admin/base.html.twig' %}

{% block title %}Onsite Course Details - Capitol Academy Admin{% endblock %}

{% block page_title %}Onsite Course Details{% endblock %}

{% block breadcrumbs %}
<li class=\"breadcrumb-item\"><a href=\"{{ path('admin_dashboard') }}\">Home</a></li>
<li class=\"breadcrumb-item\"><a href=\"{{ path('admin_onsite_courses') }}\">Onsite Courses</a></li>
<li class=\"breadcrumb-item active\">{{ course.title }}</li>
{% endblock %}

{% block content %}
<div class=\"container-fluid\">


    <!-- Integrated Header with Content -->
    <div class=\"card border-0 shadow-lg mb-4\">
        <div class=\"card-header\" style=\"background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%); color: white; padding: 1.5rem; border-radius: 15px 15px 0 0;\">
            <div class=\"row align-items-center\">
                <div class=\"col-md-6\">
                    <h2 class=\"card-title mb-0\" style=\"font-size: 1.8rem; font-weight: 600;\">
                        <i class=\"fas fa-graduation-cap mr-3\" style=\"font-size: 2rem;\"></i>
                        Onsite Course Details: {{ course.title }}
                    </h2>
                </div>
                <div class=\"col-md-6\">
                    <div class=\"d-flex justify-content-end align-items-center flex-wrap\">
                        <!-- Edit Onsite Course Button (Icon Only) -->
                        <a href=\"{{ path('admin_onsite_course_edit', {'code': course.code}) }}\"
                           class=\"btn me-2 mb-2 mb-md-0\"
                           style=\"border-radius: 50%; width: 45px; height: 45px; display: inline-flex; align-items: center; justify-content: center; background: white; border: 2px solid #011a2d; transition: all 0.3s ease;\"
                           onmouseover=\"this.style.background='#011a2d'; this.querySelector('i').style.color='white';\"
                           onmouseout=\"this.style.background='white'; this.querySelector('i').style.color='#011a2d';\"
                           title=\"Edit Onsite Course\">
                            <i class=\"fas fa-edit\" style=\"color: #011a2d;\"></i>
                        </a>

                        <!-- Print Onsite Course Button (Icon Only) -->
                        <a href=\"javascript:void(0)\" onclick=\"window.print()\"
                           class=\"btn me-2 mb-2 mb-md-0\"
                           style=\"border-radius: 50%; width: 45px; height: 45px; display: inline-flex; align-items: center; justify-content: center; background: white; border: 2px solid #011a2d; transition: all 0.3s ease;\"
                           onmouseover=\"this.style.background='#011a2d'; this.querySelector('i').style.color='white';\"
                           onmouseout=\"this.style.background='white'; this.querySelector('i').style.color='#011a2d';\"
                           title=\"Print Onsite Course Details\">
                            <i class=\"fas fa-print\" style=\"color: #011a2d;\"></i>
                        </a>

                        <!-- Toggle Status Button -->
                        <button type=\"button\"
                                class=\"btn me-2 mb-2 mb-md-0\"
                                style=\"font-weight: 600; border-radius: 8px; padding: 0.75rem 1.5rem; background: {{ course.isActive ? '#6c757d' : '#28a745' }}; color: white; border: 2px solid {{ course.isActive ? '#6c757d' : '#28a745' }}; transition: all 0.3s ease;\"
                                onclick=\"toggleOnsiteCourseStatus({{ course.id }}, '{{ course.title }}', {{ course.isActive ? 'true' : 'false' }})\">
                            <i class=\"fas fa-{{ course.isActive ? 'pause' : 'play' }} me-2\"></i>
                            {{ course.isActive ? 'Deactivate' : 'Activate' }}
                        </button>

                        <!-- Back to Onsite Courses Button -->
                        <a href=\"{{ path('admin_onsite_courses') }}\"
                           class=\"btn mb-2 mb-md-0\"
                           style=\"font-weight: 600; border-radius: 8px; padding: 0.75rem 1.5rem; background: white; color: #011a2d; border: 2px solid #011a2d; transition: all 0.3s ease;\"
                           onmouseover=\"this.style.background='#011a2d'; this.style.color='white';\"
                           onmouseout=\"this.style.background='white'; this.style.color='#011a2d';\">
                            <i class=\"fas fa-arrow-left me-2\"></i>
                            Back to Onsite Courses
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <div class=\"card-body\">
            <div class=\"row\">
                <div class=\"col-12\">
                    <!-- Course Code and Title (same line) -->
                    <div class=\"row mb-4\">
                        <div class=\"col-md-4\">
                            <div class=\"form-group\">
                                <label class=\"form-label\">
                                    <i class=\"fas fa-hashtag text-primary mr-1\"></i>
                                    Course Code
                                </label>
                                <div class=\"enhanced-display-field\" style=\"background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d;\">
                                    {{ course.code }}
                                </div>
                            </div>
                        </div>
                        <div class=\"col-md-8\">
                            <div class=\"form-group\">
                                <label class=\"form-label\">
                                    <i class=\"fas fa-graduation-cap text-primary mr-1\"></i>
                                    Course Title
                                </label>
                                <div class=\"enhanced-display-field\" style=\"background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d;\">
                                    {{ course.title }}
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Description -->
                    <div class=\"form-group mb-4\">
                        <label class=\"form-label\">
                            <i class=\"fas fa-align-left text-primary mr-1\"></i>
                            Description
                        </label>
                        <div class=\"enhanced-display-field\" style=\"background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d; min-height: 80px;\">
                            {{ course.description ?? 'No description provided' }}
                        </div>
                    </div>

                    <!-- Category and Level (same line) -->
                    <div class=\"row mb-4\">
                        <div class=\"col-md-6\">
                            <div class=\"form-group\">
                                <label class=\"form-label\">
                                    <i class=\"fas fa-folder text-primary mr-1\"></i>
                                    Category
                                </label>
                                <div class=\"enhanced-display-field\" style=\"background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d;\">
                                    {{ course.category ?? 'Uncategorized' }}
                                </div>
                            </div>
                        </div>
                        <div class=\"col-md-6\">
                            <div class=\"form-group\">
                                <label class=\"form-label\">
                                    <i class=\"fas fa-layer-group text-primary mr-1\"></i>
                                    Level
                                </label>
                                <div class=\"enhanced-display-field\" style=\"background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d;\">
                                    {{ course.level ?? 'Not specified' }}
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Learning Outcomes -->
                    {% if course.learningOutcomes and course.learningOutcomes|length > 0 %}
                    <div class=\"form-group mb-4\">
                        <label class=\"form-label\">
                            <i class=\"fas fa-bullseye text-primary mr-1\"></i>
                            Learning Outcomes
                        </label>
                        <div class=\"enhanced-display-field\" style=\"background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d;\">
                            <ul class=\"mb-0\" style=\"padding-left: 1.5rem;\">
                                {% for outcome in course.learningOutcomes %}
                                    <li class=\"mb-2\">
                                        <i class=\"fas fa-check-circle text-success me-2\"></i>
                                        {{ outcome }}
                                    </li>
                                {% endfor %}
                            </ul>
                        </div>
                    </div>
                    {% endif %}

                    <!-- Features -->
                    {% if course.features and course.features|length > 0 %}
                    <div class=\"form-group mb-4\">
                        <label class=\"form-label\">
                            <i class=\"fas fa-star text-primary mr-1\"></i>
                            Features
                        </label>
                        <div class=\"enhanced-display-field\" style=\"background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d;\">
                            <ul class=\"mb-0\" style=\"padding-left: 1.5rem;\">
                                {% for feature in course.features %}
                                    <li class=\"mb-2\">
                                        <i class=\"fas fa-star text-warning me-2\"></i>
                                        {{ feature }}
                                    </li>
                                {% endfor %}
                            </ul>
                        </div>
                    </div>
                    {% endif %}

                    <!-- Thumbnail -->
                    <div class=\"form-group mb-4\">
                        <label class=\"form-label\">
                            <i class=\"fas fa-image text-primary mr-1\"></i>
                            Course Thumbnail
                        </label>
                        <div class=\"d-flex justify-content-center\">
                            {% if course.thumbnailImage %}
                                <img src=\"{{ course.thumbnailUrl }}\"
                                     alt=\"Course Thumbnail\"
                                     class=\"img-fluid\"
                                     style=\"width: 400px; height: 225px; object-fit: cover; border: 2px solid #1e3c72; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.15);\">
                            {% else %}
                                <div class=\"d-flex align-items-center justify-content-center\"
                                     style=\"width: 400px; height: 225px; border: 2px solid #ced4da; border-radius: 8px; background: #f8f9fa;\">
                                    <div class=\"text-center text-muted\">
                                        <i class=\"fas fa-image\" style=\"font-size: 3rem; margin-bottom: 1rem;\"></i>
                                        <p>No thumbnail available</p>
                                    </div>
                                </div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- Modules Information -->
                    {% if course.hasModules %}
                    <div class=\"form-group mb-4\">
                        <label class=\"form-label\">
                            <i class=\"fas fa-puzzle-piece text-primary mr-1\"></i>
                            Course Modules
                        </label>
                        <div class=\"enhanced-display-field\" style=\"background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d;\">
                            {% if course.modules and course.modules|length > 0 %}
                                <div class=\"row\">
                                    {% for module in course.modules %}
                                    <div class=\"col-md-6 mb-3\">
                                        <div class=\"module-card\" style=\"background: white; border: 1px solid #e9ecef; border-radius: 8px; padding: 1rem;\">
                                            <h6 class=\"mb-2\" style=\"color: #011a2d; font-weight: 600;\">
                                                <i class=\"fas fa-cube me-2\"></i>{{ module.title }}
                                            </h6>
                                            {% if module.description %}
                                                <p class=\"mb-2 text-muted\" style=\"font-size: 0.9rem;\">{{ module.description|slice(0, 100) }}{% if module.description|length > 100 %}...{% endif %}</p>
                                            {% endif %}
                                            {% if module.learningOutcomes and module.learningOutcomes|length > 0 %}
                                                <small class=\"text-success\">
                                                    <i class=\"fas fa-check-circle me-1\"></i>{{ module.learningOutcomes|length }} learning outcome{{ module.learningOutcomes|length > 1 ? 's' : '' }}
                                                </small>
                                            {% endif %}
                                        </div>
                                    </div>
                                    {% endfor %}
                                </div>
                            {% else %}
                                <p class=\"mb-0 text-muted\">
                                    <i class=\"fas fa-info-circle me-2\"></i>This course has modules enabled but no modules have been created yet.
                                </p>
                            {% endif %}
                        </div>
                    </div>
                    {% endif %}

                    <!-- Status and Creation Date -->
                    <div class=\"row mb-4\">
                        <div class=\"col-md-6\">
                            <div class=\"form-group\">
                                <label class=\"form-label\">
                                    <i class=\"fas fa-toggle-on text-primary mr-1\"></i>
                                    Status
                                </label>
                                <div class=\"enhanced-display-field\" style=\"background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d; min-height: 3.5rem; display: flex; align-items: center;\">
                                    {% if course.isActive %}
                                        <span class=\"badge bg-success\" style=\"font-size: 0.9rem; padding: 0.5rem 1rem;\">
                                            <i class=\"fas fa-check-circle mr-1\"></i>Active
                                        </span>
                                    {% else %}
                                        <span class=\"badge bg-secondary\" style=\"font-size: 0.9rem; padding: 0.5rem 1rem;\">
                                            <i class=\"fas fa-pause-circle mr-1\"></i>Inactive
                                        </span>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        <div class=\"col-md-6\">
                            <div class=\"form-group\">
                                <label class=\"form-label\">
                                    <i class=\"fas fa-calendar-plus text-primary mr-1\"></i>
                                    Creation Date
                                </label>
                                <div class=\"enhanced-display-field\" style=\"background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d; min-height: 3.5rem; display: flex; align-items: center;\">
                                    {{ course.createdAt|date('F j, Y \\\\a\\\\t g:i A') }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{% endblock %}
", "admin/onsite_courses/preview.html.twig", "C:\\Users\\<USER>\\Desktop\\Capitol Academy\\Capitol-Academy\\templates\\admin\\onsite_courses\\preview.html.twig");
    }
}
