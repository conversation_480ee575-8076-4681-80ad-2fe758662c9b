<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\CoreExtension;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;
use Twig\TemplateWrapper;

/* course/list.html.twig */
class __TwigTemplate_5909bf20cd1812d28f8251e6445a0fe9 extends Template
{
    private Source $source;
    /**
     * @var array<string, Template>
     */
    private array $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->blocks = [
            'title' => [$this, 'block_title'],
            'meta_description' => [$this, 'block_meta_description'],
            'stylesheets' => [$this, 'block_stylesheets'],
            'body' => [$this, 'block_body'],
        ];
    }

    protected function doGetParent(array $context): bool|string|Template|TemplateWrapper
    {
        // line 1
        return "base.html.twig";
    }

    protected function doDisplay(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "course/list.html.twig"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "course/list.html.twig"));

        $this->parent = $this->load("base.html.twig", 1);
        yield from $this->parent->unwrap()->yield($context, array_merge($this->blocks, $blocks));
        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

    }

    // line 3
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_title(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        yield "Courses - Capitol Academy";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 5
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_meta_description(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "meta_description"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "meta_description"));

        yield "Explore our comprehensive trading courses at Capitol Academy. Learn financial markets, forex trading, cryptocurrency, and more with expert instructors.";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 7
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_stylesheets(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "stylesheets"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "stylesheets"));

        // line 8
        yield "<style>
.courses-hero {
    background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%);
    color: white;
    padding: 4rem 0;
    text-align: center;
}

.course-card {
    background: white;
    border-radius: 15px;
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    overflow: hidden;
    height: 100%;
}

.course-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0,0,0,0.15);
}

.course-thumbnail {
    width: 100%;
    height: 200px;
    object-fit: cover;
}

.course-content {
    padding: 1.5rem;
}

.course-title {
    color: #011a2d;
    font-weight: 600;
    margin-bottom: 1rem;
}

.course-description {
    color: #6c757d;
    font-size: 0.9rem;
    margin-bottom: 1.5rem;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.course-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    font-size: 0.85rem;
    color: #6c757d;
}

.course-actions {
    display: flex;
    gap: 0.5rem;
}

.btn-course-contact {
    background: #011a2d;
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    text-decoration: none;
    font-size: 0.85rem;
    transition: all 0.3s ease;
}

.btn-course-contact:hover {
    background: #a90418;
    color: white;
    text-decoration: none;
}

.btn-course-view {
    background: transparent;
    color: #011a2d;
    border: 1px solid #011a2d;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    text-decoration: none;
    font-size: 0.85rem;
    transition: all 0.3s ease;
}

.btn-course-view:hover {
    background: #011a2d;
    color: white;
    text-decoration: none;
}

.empty-state {
    text-align: center;
    padding: 4rem 2rem;
    color: #6c757d;
}

.empty-state i {
    font-size: 4rem;
    margin-bottom: 1rem;
    color: #dee2e6;
}
</style>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 117
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_body(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "body"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "body"));

        // line 118
        yield "<!-- Hero Section -->
<section class=\"courses-hero\">
    <div class=\"container\">
        <h1 class=\"display-4 mb-3\">Our Trading Courses</h1>
        <p class=\"lead\">Master the financial markets with our comprehensive onsite trading courses</p>
    </div>
</section>

<!-- Courses Section -->
<section class=\"py-5\">
    <div class=\"container\">
        ";
        // line 129
        if ((Twig\Extension\CoreExtension::length($this->env->getCharset(), (isset($context["courses"]) || array_key_exists("courses", $context) ? $context["courses"] : (function () { throw new RuntimeError('Variable "courses" does not exist.', 129, $this->source); })())) > 0)) {
            // line 130
            yield "            <div class=\"row\">
                ";
            // line 131
            $context['_parent'] = $context;
            $context['_seq'] = CoreExtension::ensureTraversable((isset($context["courses"]) || array_key_exists("courses", $context) ? $context["courses"] : (function () { throw new RuntimeError('Variable "courses" does not exist.', 131, $this->source); })()));
            foreach ($context['_seq'] as $context["_key"] => $context["course"]) {
                // line 132
                yield "                    <div class=\"col-lg-4 col-md-6 mb-4\">
                        <div class=\"course-card\">
                            ";
                // line 134
                if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, $context["course"], "thumbnailImage", [], "any", false, false, false, 134)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
                    // line 135
                    yield "                                <img src=\"";
                    yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["course"], "thumbnailUrl", [], "any", false, false, false, 135), "html", null, true);
                    yield "\" alt=\"";
                    yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["course"], "title", [], "any", false, false, false, 135), "html", null, true);
                    yield "\" class=\"course-thumbnail\">
                            ";
                } else {
                    // line 137
                    yield "                                <div class=\"course-thumbnail d-flex align-items-center justify-content-center\" style=\"background: #f8f9fa;\">
                                    <i class=\"fas fa-graduation-cap text-muted\" style=\"font-size: 3rem;\"></i>
                                </div>
                            ";
                }
                // line 141
                yield "                            
                            <div class=\"course-content\">
                                <h5 class=\"course-title\">";
                // line 143
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["course"], "title", [], "any", false, false, false, 143), "html", null, true);
                yield "</h5>
                                
                                <div class=\"course-meta\">
                                    <span><i class=\"fas fa-tag me-1\"></i>";
                // line 146
                yield (((CoreExtension::getAttribute($this->env, $this->source, $context["course"], "category", [], "any", true, true, false, 146) &&  !(null === CoreExtension::getAttribute($this->env, $this->source, $context["course"], "category", [], "any", false, false, false, 146)))) ? ($this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["course"], "category", [], "any", false, false, false, 146), "html", null, true)) : ("General"));
                yield "</span>
                                    <span><i class=\"fas fa-signal me-1\"></i>";
                // line 147
                yield (((CoreExtension::getAttribute($this->env, $this->source, $context["course"], "level", [], "any", true, true, false, 147) &&  !(null === CoreExtension::getAttribute($this->env, $this->source, $context["course"], "level", [], "any", false, false, false, 147)))) ? ($this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["course"], "level", [], "any", false, false, false, 147), "html", null, true)) : ("All Levels"));
                yield "</span>
                                </div>
                                
                                <!-- Course Description -->
                                <p class=\"course-description\">";
                // line 151
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["course"], "description", [], "any", false, false, false, 151), "html", null, true);
                yield "</p>

                                <!-- Course Actions -->
                                <div class=\"course-actions\">
                                    <a href=\"";
                // line 155
                yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("app_contact_registration");
                yield "?course=";
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["course"], "code", [], "any", false, false, false, 155), "html", null, true);
                yield "\" class=\"btn-course-contact\">
                                        <i class=\"fas fa-envelope me-2\"></i>Contact for Enrollment
                                    </a>
                                    <a href=\"";
                // line 158
                yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath(CoreExtension::getAttribute($this->env, $this->source, $context["course"], "routeName", [], "any", false, false, false, 158));
                yield "\" class=\"btn-course-view\">
                                        <i class=\"fas fa-eye me-2\"></i>View Details
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                ";
            }
            $_parent = $context['_parent'];
            unset($context['_seq'], $context['_key'], $context['course'], $context['_parent']);
            $context = array_intersect_key($context, $_parent) + $_parent;
            // line 166
            yield "            </div>
        ";
        } else {
            // line 168
            yield "            <div class=\"empty-state\">
                <i class=\"fas fa-graduation-cap\"></i>
                <h3>No Courses Available</h3>
                <p>We're currently preparing our course offerings. Please check back soon!</p>
                <a href=\"";
            // line 172
            yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("app_contact");
            yield "\" class=\"btn btn-primary mt-3\">
                    <i class=\"fas fa-envelope me-2\"></i>Contact Us for Information
                </a>
            </div>
        ";
        }
        // line 177
        yield "    </div>
</section>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName(): string
    {
        return "course/list.html.twig";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable(): bool
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo(): array
    {
        return array (  366 => 177,  358 => 172,  352 => 168,  348 => 166,  334 => 158,  326 => 155,  319 => 151,  312 => 147,  308 => 146,  302 => 143,  298 => 141,  292 => 137,  284 => 135,  282 => 134,  278 => 132,  274 => 131,  271 => 130,  269 => 129,  256 => 118,  243 => 117,  125 => 8,  112 => 7,  89 => 5,  66 => 3,  43 => 1,);
    }

    public function getSourceContext(): Source
    {
        return new Source("{% extends 'base.html.twig' %}

{% block title %}Courses - Capitol Academy{% endblock %}

{% block meta_description %}Explore our comprehensive trading courses at Capitol Academy. Learn financial markets, forex trading, cryptocurrency, and more with expert instructors.{% endblock %}

{% block stylesheets %}
<style>
.courses-hero {
    background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%);
    color: white;
    padding: 4rem 0;
    text-align: center;
}

.course-card {
    background: white;
    border-radius: 15px;
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    overflow: hidden;
    height: 100%;
}

.course-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0,0,0,0.15);
}

.course-thumbnail {
    width: 100%;
    height: 200px;
    object-fit: cover;
}

.course-content {
    padding: 1.5rem;
}

.course-title {
    color: #011a2d;
    font-weight: 600;
    margin-bottom: 1rem;
}

.course-description {
    color: #6c757d;
    font-size: 0.9rem;
    margin-bottom: 1.5rem;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.course-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    font-size: 0.85rem;
    color: #6c757d;
}

.course-actions {
    display: flex;
    gap: 0.5rem;
}

.btn-course-contact {
    background: #011a2d;
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    text-decoration: none;
    font-size: 0.85rem;
    transition: all 0.3s ease;
}

.btn-course-contact:hover {
    background: #a90418;
    color: white;
    text-decoration: none;
}

.btn-course-view {
    background: transparent;
    color: #011a2d;
    border: 1px solid #011a2d;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    text-decoration: none;
    font-size: 0.85rem;
    transition: all 0.3s ease;
}

.btn-course-view:hover {
    background: #011a2d;
    color: white;
    text-decoration: none;
}

.empty-state {
    text-align: center;
    padding: 4rem 2rem;
    color: #6c757d;
}

.empty-state i {
    font-size: 4rem;
    margin-bottom: 1rem;
    color: #dee2e6;
}
</style>
{% endblock %}

{% block body %}
<!-- Hero Section -->
<section class=\"courses-hero\">
    <div class=\"container\">
        <h1 class=\"display-4 mb-3\">Our Trading Courses</h1>
        <p class=\"lead\">Master the financial markets with our comprehensive onsite trading courses</p>
    </div>
</section>

<!-- Courses Section -->
<section class=\"py-5\">
    <div class=\"container\">
        {% if courses|length > 0 %}
            <div class=\"row\">
                {% for course in courses %}
                    <div class=\"col-lg-4 col-md-6 mb-4\">
                        <div class=\"course-card\">
                            {% if course.thumbnailImage %}
                                <img src=\"{{ course.thumbnailUrl }}\" alt=\"{{ course.title }}\" class=\"course-thumbnail\">
                            {% else %}
                                <div class=\"course-thumbnail d-flex align-items-center justify-content-center\" style=\"background: #f8f9fa;\">
                                    <i class=\"fas fa-graduation-cap text-muted\" style=\"font-size: 3rem;\"></i>
                                </div>
                            {% endif %}
                            
                            <div class=\"course-content\">
                                <h5 class=\"course-title\">{{ course.title }}</h5>
                                
                                <div class=\"course-meta\">
                                    <span><i class=\"fas fa-tag me-1\"></i>{{ course.category ?? 'General' }}</span>
                                    <span><i class=\"fas fa-signal me-1\"></i>{{ course.level ?? 'All Levels' }}</span>
                                </div>
                                
                                <!-- Course Description -->
                                <p class=\"course-description\">{{ course.description }}</p>

                                <!-- Course Actions -->
                                <div class=\"course-actions\">
                                    <a href=\"{{ path('app_contact_registration') }}?course={{ course.code }}\" class=\"btn-course-contact\">
                                        <i class=\"fas fa-envelope me-2\"></i>Contact for Enrollment
                                    </a>
                                    <a href=\"{{ path(course.routeName) }}\" class=\"btn-course-view\">
                                        <i class=\"fas fa-eye me-2\"></i>View Details
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                {% endfor %}
            </div>
        {% else %}
            <div class=\"empty-state\">
                <i class=\"fas fa-graduation-cap\"></i>
                <h3>No Courses Available</h3>
                <p>We're currently preparing our course offerings. Please check back soon!</p>
                <a href=\"{{ path('app_contact') }}\" class=\"btn btn-primary mt-3\">
                    <i class=\"fas fa-envelope me-2\"></i>Contact Us for Information
                </a>
            </div>
        {% endif %}
    </div>
</section>
{% endblock %}
", "course/list.html.twig", "C:\\Users\\<USER>\\Desktop\\Capitol Academy\\Capitol-Academy\\templates\\course\\list.html.twig");
    }
}
