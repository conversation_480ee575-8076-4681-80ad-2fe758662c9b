{% extends 'base.html.twig' %}

{% block title %}Courses - Capitol Academy{% endblock %}

{% block meta_description %}Explore our comprehensive trading courses at Capitol Academy. Learn financial markets, forex trading, cryptocurrency, and more with expert instructors.{% endblock %}

{% block stylesheets %}
<style>
.courses-hero {
    background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%);
    color: white;
    padding: 4rem 0;
    text-align: center;
}

.course-card {
    background: white;
    border-radius: 15px;
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    overflow: hidden;
    height: 100%;
}

.course-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0,0,0,0.15);
}

.course-thumbnail {
    width: 100%;
    height: 200px;
    object-fit: cover;
}

.course-content {
    padding: 1.5rem;
}

.course-title {
    color: #011a2d;
    font-weight: 600;
    margin-bottom: 1rem;
}

.course-description {
    color: #6c757d;
    font-size: 0.9rem;
    margin-bottom: 1.5rem;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.course-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    font-size: 0.85rem;
    color: #6c757d;
}

.course-actions {
    display: flex;
    gap: 0.5rem;
}

.btn-course-contact {
    background: #011a2d;
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    text-decoration: none;
    font-size: 0.85rem;
    transition: all 0.3s ease;
}

.btn-course-contact:hover {
    background: #a90418;
    color: white;
    text-decoration: none;
}

.btn-course-view {
    background: transparent;
    color: #011a2d;
    border: 1px solid #011a2d;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    text-decoration: none;
    font-size: 0.85rem;
    transition: all 0.3s ease;
}

.btn-course-view:hover {
    background: #011a2d;
    color: white;
    text-decoration: none;
}

.empty-state {
    text-align: center;
    padding: 4rem 2rem;
    color: #6c757d;
}

.empty-state i {
    font-size: 4rem;
    margin-bottom: 1rem;
    color: #dee2e6;
}
</style>
{% endblock %}

{% block body %}
<!-- Hero Section -->
<section class="courses-hero">
    <div class="container">
        <h1 class="display-4 mb-3">Our Trading Courses</h1>
        <p class="lead">Master the financial markets with our comprehensive onsite trading courses</p>
    </div>
</section>

<!-- Courses Section -->
<section class="py-5">
    <div class="container">
        {% if courses|length > 0 %}
            <div class="row">
                {% for course in courses %}
                    <div class="col-lg-4 col-md-6 mb-4">
                        <div class="course-card">
                            {% if course.thumbnailImage %}
                                <img src="{{ course.thumbnailUrl }}" alt="{{ course.title }}" class="course-thumbnail">
                            {% else %}
                                <div class="course-thumbnail d-flex align-items-center justify-content-center" style="background: #f8f9fa;">
                                    <i class="fas fa-graduation-cap text-muted" style="font-size: 3rem;"></i>
                                </div>
                            {% endif %}
                            
                            <div class="course-content">
                                <h5 class="course-title">{{ course.title }}</h5>
                                
                                <div class="course-meta">
                                    <span><i class="fas fa-tag me-1"></i>{{ course.category ?? 'General' }}</span>
                                    <span><i class="fas fa-signal me-1"></i>{{ course.level ?? 'All Levels' }}</span>
                                </div>
                                
                                <!-- Course Description -->
                                <p class="course-description">{{ course.description }}</p>

                                <!-- Course Actions -->
                                <div class="course-actions">
                                    <a href="{{ path('app_contact_registration') }}?course={{ course.code }}" class="btn-course-contact">
                                        <i class="fas fa-envelope me-2"></i>Contact for Enrollment
                                    </a>
                                    <a href="{{ path(course.routeName) }}" class="btn-course-view">
                                        <i class="fas fa-eye me-2"></i>View Details
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                {% endfor %}
            </div>
        {% else %}
            <div class="empty-state">
                <i class="fas fa-graduation-cap"></i>
                <h3>No Courses Available</h3>
                <p>We're currently preparing our course offerings. Please check back soon!</p>
                <a href="{{ path('app_contact') }}" class="btn btn-primary mt-3">
                    <i class="fas fa-envelope me-2"></i>Contact Us for Information
                </a>
            </div>
        {% endif %}
    </div>
</section>
{% endblock %}
