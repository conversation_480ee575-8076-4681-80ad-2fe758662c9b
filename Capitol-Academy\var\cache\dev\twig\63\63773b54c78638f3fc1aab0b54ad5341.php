<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\CoreExtension;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;
use Twig\TemplateWrapper;

/* admin/onsite_courses/create.html.twig */
class __TwigTemplate_ac43359ba18ef43c44d76997ebf51848 extends Template
{
    private Source $source;
    /**
     * @var array<string, Template>
     */
    private array $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->blocks = [
            'title' => [$this, 'block_title'],
            'page_title' => [$this, 'block_page_title'],
            'breadcrumbs' => [$this, 'block_breadcrumbs'],
            'content' => [$this, 'block_content'],
            'javascripts' => [$this, 'block_javascripts'],
        ];
    }

    protected function doGetParent(array $context): bool|string|Template|TemplateWrapper
    {
        // line 1
        return "admin/base.html.twig";
    }

    protected function doDisplay(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/onsite_courses/create.html.twig"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/onsite_courses/create.html.twig"));

        $this->parent = $this->load("admin/base.html.twig", 1);
        yield from $this->parent->unwrap()->yield($context, array_merge($this->blocks, $blocks));
        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

    }

    // line 3
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_title(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        yield "Create Onsite Course - Capitol Academy Admin";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 5
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_page_title(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "page_title"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "page_title"));

        yield "Create New Onsite Course";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 7
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_breadcrumbs(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "breadcrumbs"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "breadcrumbs"));

        // line 8
        yield "<li class=\"breadcrumb-item\"><a href=\"";
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_dashboard");
        yield "\">Home</a></li>
<li class=\"breadcrumb-item\"><a href=\"";
        // line 9
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_onsite_courses");
        yield "\">Onsite Courses</a></li>
<li class=\"breadcrumb-item active\">Create Onsite Course</li>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 13
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_content(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "content"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "content"));

        // line 14
        yield "<div class=\"container-fluid\">


    <!-- Integrated Header with Content -->
    <div class=\"card border-0 shadow-lg mb-4\">
        <div class=\"card-header\" style=\"background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%); color: white; padding: 1.5rem; border-radius: 15px 15px 0 0;\">
            <div class=\"row align-items-center\">
                <div class=\"col-md-6\">
                    <h2 class=\"card-title mb-0\" style=\"font-size: 1.8rem; font-weight: 600;\">
                        <i class=\"fas fa-graduation-cap mr-3\" style=\"font-size: 2rem;\"></i>
                        Create New Onsite Course
                    </h2>
                </div>
                <div class=\"col-md-6\">
                    <div class=\"d-flex justify-content-end align-items-center flex-wrap\">
                        <!-- Back to Onsite Courses Button -->
                        <a href=\"";
        // line 30
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_onsite_courses");
        yield "\"
                           class=\"btn mb-2 mb-md-0\"
                           style=\"font-weight: 600; border-radius: 8px; padding: 0.75rem 1.5rem; background: white; color: #011a2d; border: 2px solid #011a2d; transition: all 0.3s ease;\"
                           onmouseover=\"this.style.background='#011a2d'; this.style.color='white';\"
                           onmouseout=\"this.style.background='white'; this.style.color='#011a2d';\">
                            <i class=\"fas fa-arrow-left me-2\"></i>
                            Back to Onsite Courses
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <form method=\"post\" id=\"course-form\" class=\"needs-validation\" enctype=\"multipart/form-data\" novalidate>
            <input type=\"hidden\" name=\"_token\" value=\"";
        // line 44
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->env->getRuntime('Symfony\Component\Form\FormRenderer')->renderCsrfToken("course_create"), "html", null, true);
        yield "\">
            <input type=\"hidden\" name=\"is_active\" value=\"1\">
            <div class=\"card-body\">
                    <!-- Single Column Layout -->
                    <div class=\"row\">
                        <div class=\"col-12\">
                            <!-- Course Code and Title Row -->
                            <div class=\"row\">
                                <!-- Course Code -->
                                <div class=\"col-md-6\">
                                    <div class=\"form-group\">
                                        <label for=\"code\" class=\"form-label\">
                                            <i class=\"fas fa-hashtag\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                            Onsite Course Code <span class=\"text-danger\">*</span>
                                        </label>
                                        <input type=\"text\"
                                               class=\"form-control enhanced-field\"
                                               id=\"code\"
                                               name=\"code\"
                                               placeholder=\"e.g., OSC101, TRAD200\"
                                               required
                                               maxlength=\"10\"
                                               pattern=\"[A-Za-z]{2,4}[0-9]{1,4}\"
                                               title=\"Format: 2-4 letters followed by 1-4 numbers (e.g., OSC101, TRAD200)\"
                                               style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; border-radius: 0.375rem;\">
                                        <div class=\"invalid-feedback\">
                                            Please provide a valid onsite course code (e.g., OSC101, TRAD200).
                                        </div>
                                    </div>
                                </div>

                                <!-- Course Title -->
                                <div class=\"col-md-6\">
                                    <div class=\"form-group\">
                                        <label for=\"title\" class=\"form-label\">
                                            <i class=\"fas fa-graduation-cap\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                            Onsite Course Title <span class=\"text-danger\">*</span>
                                        </label>
                                        <input type=\"text\"
                                               class=\"form-control enhanced-field\"
                                               id=\"title\"
                                               name=\"title\"
                                               placeholder=\"Enter comprehensive onsite course title\"
                                               required
                                               maxlength=\"255\"
                                               style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; border-radius: 0.375rem;\">
                                        <div class=\"invalid-feedback\">
                                            Please provide an onsite course title.
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Course Category and Level Row -->
                            <div class=\"row\">
                                <!-- Course Category -->
                                <div class=\"col-md-6\">
                                    <div class=\"form-group\">
                                        <label for=\"category\" class=\"form-label\">
                                            <i class=\"fas fa-folder text-primary mr-1\" aria-hidden=\"true\"></i>
                                            Category
                                        </label>
                                        <select class=\"form-select enhanced-dropdown\"
                                                id=\"category\"
                                                name=\"category\"
                                                aria-describedby=\"category_help category_error\"
                                                aria-label=\"Select a course category\"
                                                style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; background-color: #fff;\">
                                            <option value=\"\">Choose a category...</option>
                                            ";
        // line 113
        $context['_parent'] = $context;
        $context['_seq'] = CoreExtension::ensureTraversable((isset($context["categories"]) || array_key_exists("categories", $context) ? $context["categories"] : (function () { throw new RuntimeError('Variable "categories" does not exist.', 113, $this->source); })()));
        foreach ($context['_seq'] as $context["_key"] => $context["category"]) {
            // line 114
            yield "                                                <option value=\"";
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["category"], "name", [], "any", false, false, false, 114), "html", null, true);
            yield "\" ";
            yield (((CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 114, $this->source); })()), "category", [], "any", false, false, false, 114) == CoreExtension::getAttribute($this->env, $this->source, $context["category"], "name", [], "any", false, false, false, 114))) ? ("selected") : (""));
            yield ">";
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["category"], "name", [], "any", false, false, false, 114), "html", null, true);
            yield "</option>
                                            ";
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['_key'], $context['category'], $context['_parent']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        // line 116
        yield "                                        </select>
                                        <div id=\"category_help\" class=\"form-text text-muted\" style=\"display: none;\">
                                            Select the category that best describes this course. Use arrow keys to navigate options.
                                        </div>
                                        <div id=\"category_error\" class=\"invalid-feedback\" role=\"alert\" aria-live=\"polite\">
                                            Please select a category.
                                        </div>
                                    </div>
                                </div>

                                <!-- Course Level -->
                                <div class=\"col-md-6\">
                                    <div class=\"form-group\">
                                        <label for=\"level\" class=\"form-label\">
                                            <i class=\"fas fa-layer-group text-primary mr-1\" aria-hidden=\"true\"></i>
                                            Level
                                        </label>
                                        <select class=\"form-select enhanced-dropdown\"
                                                id=\"level\"
                                                name=\"level\"
                                                aria-describedby=\"level_help level_error\"
                                                aria-label=\"Select a course difficulty level\"
                                                style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; background-color: #fff;\">
                                            <option value=\"\">Choose a level...</option>
                                            <option value=\"Beginner\" ";
        // line 140
        yield (((CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 140, $this->source); })()), "level", [], "any", false, false, false, 140) == "Beginner")) ? ("selected") : (""));
        yield ">Beginner</option>
                                            <option value=\"Intermediate\" ";
        // line 141
        yield (((CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 141, $this->source); })()), "level", [], "any", false, false, false, 141) == "Intermediate")) ? ("selected") : (""));
        yield ">Intermediate</option>
                                            <option value=\"Advanced\" ";
        // line 142
        yield (((CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 142, $this->source); })()), "level", [], "any", false, false, false, 142) == "Advanced")) ? ("selected") : (""));
        yield ">Advanced</option>
                                        </select>
                                        <div id=\"level_help\" class=\"form-text text-muted\" style=\"display: none;\">
                                            Select the difficulty level that best matches this course content.
                                        </div>
                                        <div id=\"level_error\" class=\"invalid-feedback\" role=\"alert\" aria-live=\"polite\">
                                            Please select a level.
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Course Description -->
                            <div class=\"form-group\">
                                <label for=\"description\" class=\"form-label\">
                                    <i class=\"fas fa-align-left\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                    Onsite Course Description <span class=\"text-danger\">*</span>
                                </label>
                                <textarea class=\"form-control enhanced-field\"
                                          id=\"description\"
                                          name=\"description\"
                                          rows=\"8\"
                                          placeholder=\"Enter comprehensive onsite course description including objectives, target audience, and key topics...\"
                                          required
                                          style=\"min-height: 150px; font-size: 1rem; border: 2px solid #ced4da; border-radius: 0.375rem;\">";
        // line 166
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 166, $this->source); })()), "description", [], "any", false, false, false, 166), "html", null, true);
        yield "</textarea>
                                <div class=\"invalid-feedback\">
                                    Please provide a comprehensive onsite course description.
                                </div>
                            </div>

                            <!-- Learning Outcomes -->
                            <div class=\"form-group\">
                                <label class=\"form-label\">
                                    <i class=\"fas fa-graduation-cap\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                    Learning Outcomes <span class=\"text-danger\">*</span>
                                </label>

                                <div id=\"learning-outcomes-container\">
                                    <div class=\"input-group mb-2 learning-outcome-item\">
                                        <input type=\"text\"
                                               class=\"form-control enhanced-field\"
                                               name=\"learning_outcomes[]\"
                                               placeholder=\"e.g., Master fundamental trading concepts and strategies\"
                                               required
                                               style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; border-radius: 0.375rem 0 0 0.375rem;\">
                                        <div class=\"input-group-append\">
                                            <button type=\"button\" class=\"btn add-learning-outcome\" style=\"background: #28a745; color: white; border: 2px solid #28a745; border-radius: 0 0.375rem 0.375rem 0; height: calc(1.6em + 1.25rem + 4px);\">
                                                <i class=\"fas fa-plus\"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                <div class=\"invalid-feedback\">
                                    Please provide at least one learning outcome.
                                </div>
                            </div>

                            <!-- Course Features -->
                            <div class=\"form-group\">
                                <label class=\"form-label\">
                                    <i class=\"fas fa-star\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                    Course Features <span class=\"text-danger\">*</span>
                                </label>

                                <div id=\"features-container\">
                                    <div class=\"input-group mb-2 feature-item\">
                                        <input type=\"text\"
                                               class=\"form-control enhanced-field\"
                                               name=\"features[]\"
                                               placeholder=\"e.g., Hands-on practical sessions, Expert instructor guidance, Course materials included\"
                                               required
                                               style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; border-radius: 0.375rem 0 0 0.375rem;\">
                                        <div class=\"input-group-append\">
                                            <button type=\"button\" class=\"btn add-feature\" style=\"background: #28a745; color: white; border: 2px solid #28a745; border-radius: 0 0.375rem 0.375rem 0; height: calc(1.6em + 1.25rem + 4px);\">
                                                <i class=\"fas fa-plus\"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                <div class=\"invalid-feedback\">
                                    Please provide at least one course feature.
                                </div>
                            </div>
                            <!-- Thumbnail Image (Full Width) -->
                            <div class=\"form-group\">
                                <label for=\"thumbnail_image\" class=\"form-label\">
                                    <i class=\"fas fa-image\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>Thumbnail Image <span class=\"text-danger\">*</span>
                                </label>
                                <input type=\"file\"
                                       class=\"form-control enhanced-file-field\"
                                       id=\"thumbnail_image\"
                                       name=\"thumbnail_image\"
                                       accept=\"image/jpeg,image/png,image/jpg\"
                                       required
                                       style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; background-color: #fff; border-radius: 0.375rem;\">

                                <div class=\"image-preview mt-3 text-center\" id=\"thumbnail-preview\" style=\"display: none;\">
                                    <div class=\"professional-image-container mx-auto\" style=\"width: 300px; height: 200px; border: 2px solid #1e3c72; border-radius: 8px; overflow: hidden; background: #f8f9fa; box-shadow: 0 2px 8px rgba(0,0,0,0.1);\">
                                        <img src=\"\" alt=\"Thumbnail Preview\" class=\"w-100 h-100\" style=\"object-fit: cover;\">
                                    </div>
                                </div>
                            </div>

                            <!-- Has Modules -->
                            <div class=\"form-group\" style=\"margin-bottom: 1.5rem;\">
                                <div class=\"form-check form-switch\" style=\"padding-left: 0; margin-left: 0;\">
                                    <input type=\"checkbox\"
                                           class=\"form-check-input\"
                                           id=\"has_modules\"
                                           name=\"has_modules\"
                                           value=\"1\"
                                           style=\"transform: scale(1.2); margin-left: 0;\">
                                    <label class=\"form-check-label\" for=\"has_modules\" style=\"margin-left: 2.5rem; padding-top: 0.125rem;\">
                                        <i class=\"fas fa-list\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                        <strong>Enable Course Modules</strong>
                                    </label>
                                </div>
                            </div>
                            <!-- Course Modules Management Section -->
                            <div id=\"modules-section\" class=\"form-group\" style=\"display: none;\">
                                <label class=\"form-label\">
                                    <i class=\"fas fa-puzzle-piece\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                    Course Modules
                                </label>

                                <div id=\"modules-container\">
                                    <div class=\"module-item\" style=\"border: 2px solid #007bff; border-radius: 12px; padding: 1.5rem; margin-bottom: 1.5rem; background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);\">
                                        <div class=\"module-header d-flex justify-content-between align-items-center mb-3\">
                                            <h6 class=\"mb-0\" style=\"color: #007bff; font-weight: 700;\">
                                                <i class=\"fas fa-cube\" style=\"margin-right: 0.5rem;\"></i>
                                                Module <span class=\"module-number\">1</span>
                                            </h6>
                                            <button type=\"button\" class=\"btn btn-sm btn-outline-danger remove-module\" style=\"border-radius: 50%; width: 35px; height: 35px;\">
                                                <i class=\"fas fa-times\"></i>
                                            </button>
                                        </div>

                                        <!-- Module Basic Info -->
                                        <div class=\"row\">
                                            <div class=\"col-md-6\">
                                                <div class=\"form-group\">
                                                    <label class=\"form-label\">
                                                        <i class=\"fas fa-hashtag\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                                        Module Code
                                                    </label>
                                                    <input type=\"text\" class=\"form-control enhanced-field module-code\" name=\"modules[0][code]\" placeholder=\"e.g., Module-1\" style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; border-radius: 0.375rem;\">
                                                </div>
                                            </div>
                                            <div class=\"col-md-6\">
                                                <div class=\"form-group\">
                                                    <label class=\"form-label\">
                                                        <i class=\"fas fa-tag\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                                        Module Title <span class=\"text-danger\">*</span>
                                                    </label>
                                                    <input type=\"text\" class=\"form-control enhanced-field module-title\" name=\"modules[0][title]\" placeholder=\"e.g., Introduction to Trading Fundamentals\" required style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; border-radius: 0.375rem;\">
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Module Description -->
                                        <div class=\"form-group\">
                                            <label class=\"form-label\">
                                                <i class=\"fas fa-align-left\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                                Module Description <span class=\"text-danger\">*</span>
                                            </label>
                                            <textarea class=\"form-control enhanced-field module-description\" name=\"modules[0][description]\" rows=\"6\" placeholder=\"Detailed description of what this module covers...\" required style=\"min-height: 100px;\"></textarea>
                                        </div>

                                        <!-- Module Learning Outcomes -->
                                        <div class=\"form-group\">
                                            <label class=\"form-label\">
                                                <i class=\"fas fa-graduation-cap\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                                Learning Outcomes <span class=\"text-danger\">*</span>
                                            </label>
                                            <div class=\"learning-outcomes-container\">
                                                <div class=\"input-group mb-2\">
                                                    <input type=\"text\" class=\"form-control enhanced-field\" name=\"modules[0][learning_outcomes][]\" placeholder=\"e.g., Master advanced chart analysis techniques\" required style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; border-radius: 0.375rem 0 0 0.375rem;\">
                                                    <div class=\"input-group-append\">
                                                        <button type=\"button\" class=\"btn btn-success add-outcome\" style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0 0.375rem 0.375rem 0;\">
                                                            <i class=\"fas fa-plus\"></i>
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Module Features -->
                                        <div class=\"form-group\">
                                            <label class=\"form-label\">
                                                <i class=\"fas fa-star\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                                Features <span class=\"text-danger\">*</span>
                                            </label>
                                            <div class=\"features-container\">
                                                <div class=\"input-group mb-2\">
                                                    <input type=\"text\" class=\"form-control enhanced-field\" name=\"modules[0][features][]\" placeholder=\"e.g., Interactive practical exercises\" required style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; border-radius: 0.375rem 0 0 0.375rem;\">
                                                    <div class=\"input-group-append\">
                                                        <button type=\"button\" class=\"btn btn-success add-feature\" style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0 0.375rem 0.375rem 0;\">
                                                            <i class=\"fas fa-plus\"></i>
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Hidden fields -->
                                        <input type=\"hidden\" name=\"modules[0][is_active]\" value=\"1\">
                                        <input type=\"hidden\" name=\"modules[0][sort_order]\" value=\"1\">
                                    </div>
                                </div>

                                <button type=\"button\" id=\"add-module\" class=\"btn btn-outline-primary\">
                                    <i class=\"fas fa-plus\" style=\"margin-right: 0.5rem;\"></i>
                                    Add Another Module
                                </button>
                            </div>

                        </div>

                        <!-- Action Buttons Section -->
                                    <div class=\"d-flex justify-content-between align-items-center flex-wrap gap-3\">
                                        <button type=\"submit\" class=\"btn btn-lg btn-success\" style=\"background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; border: none; font-weight: 600; border-radius: 8px; padding: 1rem 2.5rem; transition: all 0.3s ease; min-width: 200px;\">
                                            <i class=\"fas fa-save mr-2\"></i>
                                            Create Onsite Course
                                        </button>
                                        <a href=\"";
        // line 366
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_onsite_courses");
        yield "\" class=\"btn btn-lg btn-secondary\" style=\"font-weight: 600; border-radius: 8px; padding: 1rem 2.5rem; min-width: 200px; background: #6c757d; border-color: #6c757d;\">
                                            <i class=\"fas fa-times mr-2\"></i>
                                            Cancel
                                        </a>
                                    </div>

                    </div>
                </div>

            </div>
        </form>
    </div>
</div>

<!-- Professional Module Removal Modal -->
<div class=\"modal fade\" id=\"courseValidationModal\" tabindex=\"-1\" aria-labelledby=\"courseValidationModalLabel\" aria-hidden=\"true\">
    <div class=\"modal-dialog modal-dialog-centered\">
        <div class=\"modal-content border-0 shadow-lg\">
            <div class=\"modal-header\" style=\"background: linear-gradient(135deg, #dc3545 0%, #c82333 100%); color: white; border: none;\">
                <h5 class=\"modal-title\" id=\"courseValidationModalLabel\">
                    <i class=\"fas fa-exclamation-triangle me-2\"></i>Cannot Remove Module
                </h5>
                <button type=\"button\" class=\"btn-close btn-close-white\" data-bs-dismiss=\"modal\" aria-label=\"Close\"></button>
            </div>
            <div class=\"modal-body text-center py-4\">
                <div class=\"mb-3\">
                    <i class=\"fas fa-info-circle text-danger\" style=\"font-size: 3rem;\"></i>
                </div>
                <h6 class=\"mt-3 mb-2\">Module Required</h6>
                <p class=\"text-muted mb-3\"><strong>Warning:</strong> A course must have at least one module.</p>
            </div>
            <div class=\"modal-footer border-0 justify-content-center\">
                <button type=\"button\" class=\"btn btn-primary\" data-bs-dismiss=\"modal\">
                    <i class=\"fas fa-check me-2\"></i>Understood
                </button>
            </div>
        </div>
    </div>
</div>
    <!-- Module Validation Modal -->
    <div class=\"modal fade\" id=\"moduleValidationModal\" tabindex=\"-1\" aria-labelledby=\"moduleValidationModalLabel\" aria-hidden=\"true\">
        <div class=\"modal-dialog modal-dialog-centered modal-sm\">
            <div class=\"modal-content\" style=\"border: none; border-radius: 8px; box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);\">
                <div class=\"modal-header\" style=\"background: linear-gradient(135deg, #a90418 0%, #8b0314 100%); color: white; border: none; padding: 1rem;\">
                    <h6 class=\"modal-title\" id=\"moduleValidationModalLabel\" style=\"font-weight: 600;\">
                        <i class=\"fas fa-exclamation-triangle me-2\"></i>Module Required
                    </h6>
                    <button type=\"button\" class=\"btn-close btn-close-white\" data-bs-dismiss=\"modal\" aria-label=\"Close\"></button>
                </div>
                <div class=\"modal-body\" style=\"padding: 1rem; text-align: center;\">
                    <p class=\"mb-3\" style=\"color: #a90418;\">
                        At least one module is required.
                    </p>
                    <small class=\"text-muted\">Please add a module or disable the \"Enable Course Modules\" option.</small>
                </div>
                <div class=\"modal-footer\" style=\"border: none; padding: 1rem; background: #f8f9fa;\">
                    <button type=\"button\" class=\"btn btn-primary btn-sm\" data-bs-dismiss=\"modal\" style=\"transition: all 0.3s ease;\">
                        <i class=\"fas fa-check me-1\"></i>OK
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 432
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_javascripts(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "javascripts"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "javascripts"));

        // line 433
        yield "<script>
\$(document).ready(function() {
    // Function to toggle module field validation based on \"Course Has Modules\" state
    function toggleModuleValidation() {
        var hasModules = \$('#has_modules').is(':checked');
        var moduleInputs = \$('#modules-section input[name*=\"modules\"], #modules-section textarea[name*=\"modules\"]');

        if (hasModules) {
            // Enable validation for module fields
            moduleInputs.each(function() {
                \$(this).attr('required', true);
            });
        } else {
            // Disable validation for module fields
            moduleInputs.each(function() {
                \$(this).removeAttr('required');
                // Clear any validation state
                \$(this).removeClass('is-invalid is-valid');
                \$(this).closest('.form-group').find('.invalid-feedback').hide();
            });
        }
    }

    // Bootstrap form validation
    (function() {
        'use strict';
        window.addEventListener('load', function() {
            var forms = document.getElementsByClassName('needs-validation');
            var validation = Array.prototype.filter.call(forms, function(form) {
                form.addEventListener('submit', function(event) {
                    console.log('Form submission attempted');

                    var submitBtn = form.querySelector('button[type=\"submit\"]');

                    // Update module validation based on toggle state before validation
                    toggleModuleValidation();

                    // Check if form is valid using native HTML5 validation
                    if (form.checkValidity() === false) {
                        console.log('Form validation failed');
                        event.preventDefault();
                        event.stopPropagation();

                        // Reset button state on validation failure
                        if (submitBtn) {
                            submitBtn.disabled = false;
                            submitBtn.innerHTML = '<i class=\"fas fa-save mr-2\"></i>Create Onsite Course';
                        }

                        // Show help text when validation fails
                        \$('.help-text').show();
                    } else {
                        console.log('Form validation passed, submitting...');
                        // Show loading state on successful validation
                        if (submitBtn) {
                            submitBtn.disabled = true;
                            submitBtn.innerHTML = '<i class=\"fas fa-spinner fa-spin mr-2\"></i>Creating Onsite Course...';
                        }

                        // Hide help text when form is valid
                        \$('.help-text').hide();
                    }

                    // Add validation classes for styling
                    form.classList.add('was-validated');
                }, false);
            });
        }, false);
    })();

    // Initialize module validation state on page load
    \$(document).ready(function() {
        toggleModuleValidation();
    });

    // Auto-generate course code suggestion
    \$('#title').on('input', function() {
        var title = \$(this).val();
        if (title.length > 0 && \$('#code').val() === '') {
            var words = title.split(' ');
            var suggestion = '';
            if (words.length >= 2) {
                suggestion = 'OSC' + words[0].substring(0, 1).toUpperCase() +
                           words[1].substring(0, 1).toUpperCase() +
                           '01';
            } else {
                suggestion = 'OSC' + title.substring(0, 3).toUpperCase() + '01';
            }
            \$('#code').attr('placeholder', 'Suggestion: ' + suggestion);
        }
    });

    // Price formatting
    \$('#price').on('blur', function() {
        var value = parseFloat(\$(this).val());
        if (!isNaN(value)) {
            \$(this).val(value.toFixed(2));
        }
    });

    // Form enhancement animations
    \$('.form-control').on('focus', function() {
        \$(this).closest('.form-group').addClass('focused');
    }).on('blur', function() {
        \$(this).closest('.form-group').removeClass('focused');
    });

    // Dynamic Learning Outcomes Management
    \$(document).on('click', '.add-learning-outcome', function() {
        var container = \$('#learning-outcomes-container');
        var newItem = `
            <div class=\"input-group mb-2 learning-outcome-item\">
                <input type=\"text\"
                       class=\"form-control enhanced-field\"
                       name=\"learning_outcomes[]\"
                       placeholder=\"Enter a learning outcome...\"
                       required
                       style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;\">
                <div class=\"input-group-append\">
                    <button type=\"button\" class=\"btn btn-danger remove-learning-outcome\">
                        <i class=\"fas fa-minus\"></i>
                    </button>
                    <button type=\"button\" class=\"btn btn-success add-learning-outcome\">
                        <i class=\"fas fa-plus\"></i>
                    </button>
                </div>
            </div>
        `;
        container.append(newItem);
    });

    \$(document).on('click', '.remove-learning-outcome', function() {
        \$(this).closest('.learning-outcome-item').remove();
    });

    // Dynamic Features Management
    \$(document).on('click', '.add-feature', function() {
        var container = \$('#features-container');
        var newItem = `
            <div class=\"input-group mb-2 feature-item\">
                <input type=\"text\"
                       class=\"form-control enhanced-field\"
                       name=\"features[]\"
                       placeholder=\"e.g., Hands-on practical sessions, Expert guidance...\"
                       style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;\">
                <div class=\"input-group-append\">
                    <button type=\"button\" class=\"btn btn-danger remove-feature\">
                        <i class=\"fas fa-minus\"></i>
                    </button>
                    <button type=\"button\" class=\"btn btn-success add-feature\">
                        <i class=\"fas fa-plus\"></i>
                    </button>
                </div>
            </div>
        `;
        container.append(newItem);
    });

    \$(document).on('click', '.remove-feature', function() {
        \$(this).closest('.feature-item').remove();
    });

    // Image Preview Functionality
    \$('#thumbnail_image').on('change', function() {
        previewImage(this, '#thumbnail-preview');
    });

    \$('#banner_image').on('change', function() {
        previewImage(this, '#banner-preview');
    });

    function previewImage(input, previewSelector) {
        if (input.files && input.files[0]) {
            var reader = new FileReader();
            reader.onload = function(e) {
                \$(previewSelector).show();
                \$(previewSelector + ' img').attr('src', e.target.result);
            };
            reader.readAsDataURL(input.files[0]);
        } else {
            \$(previewSelector).hide();
        }
    }

    // Module management
    var moduleIndex = 1;

    // Toggle module section visibility
    \$('#has_modules').on('change', function() {
        if (\$(this).is(':checked')) {
            \$('#modules-section').slideDown();
        } else {
            \$('#modules-section').slideUp();
        }
        toggleModuleValidation();
    });

    // Function to toggle module field validation based on \"Course Has Modules\" state
    function toggleModuleValidation() {
        var hasModules = \$('#has_modules').is(':checked');
        var moduleInputs = \$('#modules-section input[name*=\"modules\"], #modules-section textarea[name*=\"modules\"]');

        if (hasModules) {
            moduleInputs.attr('required', true);
        } else {
            moduleInputs.removeAttr('required');
        }
    }

    \$('#add-module').on('click', function() {
        var newModule = `
            <div class=\"module-item\" style=\"border: 2px solid #007bff; border-radius: 12px; padding: 1.5rem; margin-bottom: 1.5rem; background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);\">
                <div class=\"module-header d-flex justify-content-between align-items-center mb-3\">
                    <h6 class=\"mb-0\" style=\"color: #007bff; font-weight: 700;\">
                        <i class=\"fas fa-cube\" style=\"margin-right: 0.5rem;\"></i>
                        Module <span class=\"module-number\">\${moduleIndex + 1}</span>
                    </h6>
                    <button type=\"button\" class=\"btn btn-sm btn-outline-danger remove-module\" style=\"border-radius: 50%; width: 35px; height: 35px;\">
                        <i class=\"fas fa-times\"></i>
                    </button>
                </div>

                <!-- Module Basic Info -->
                <div class=\"row\">
                    <div class=\"col-md-6\">
                        <div class=\"form-group\">
                            <label class=\"form-label\">
                                <i class=\"fas fa-hashtag\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                Module Code
                            </label>
                            <input type=\"text\" class=\"form-control enhanced-field module-code\" name=\"modules[\${moduleIndex}][code]\" placeholder=\"e.g., Module-\${moduleIndex + 1}\" style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; border-radius: 0.375rem;\">
                        </div>
                    </div>
                    <div class=\"col-md-6\">
                        <div class=\"form-group\">
                            <label class=\"form-label\">
                                <i class=\"fas fa-tag\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                Module Title <span class=\"text-danger\">*</span>
                            </label>
                            <input type=\"text\" class=\"form-control enhanced-field module-title\" name=\"modules[\${moduleIndex}][title]\" placeholder=\"e.g., Advanced Trading Strategies\" required style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; border-radius: 0.375rem;\">
                        </div>
                    </div>
                </div>

                <!-- Module Description -->
                <div class=\"form-group\">
                    <label class=\"form-label\">
                        <i class=\"fas fa-align-left\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                        Module Description <span class=\"text-danger\">*</span>
                    </label>
                    <textarea class=\"form-control enhanced-field module-description\" name=\"modules[\${moduleIndex}][description]\" rows=\"6\" placeholder=\"Detailed description of what this module covers...\" required style=\"min-height: 150px;\"></textarea>
                </div>

                <!-- Module Learning Outcomes -->
                <div class=\"form-group\">
                    <label class=\"form-label\">
                        <i class=\"fas fa-graduation-cap\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                        Learning Outcomes <span class=\"text-danger\">*</span>
                    </label>
                    <div class=\"learning-outcomes-container\">
                        <div class=\"input-group mb-2\">
                            <input type=\"text\" class=\"form-control enhanced-field\" name=\"modules[\${moduleIndex}][learning_outcomes][]\" placeholder=\"e.g., Master advanced chart analysis techniques\" required style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; border-radius: 0.375rem 0 0 0.375rem;\">
                            <div class=\"input-group-append\">
                                <button type=\"button\" class=\"btn btn-success add-outcome\" style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0 0.375rem 0.375rem 0;\">
                                    <i class=\"fas fa-plus\"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Module Features -->
                <div class=\"form-group\">
                    <label class=\"form-label\">
                        <i class=\"fas fa-star\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                        Features <span class=\"text-danger\">*</span>
                    </label>
                    <div class=\"features-container\">
                        <div class=\"input-group mb-2\">
                            <input type=\"text\" class=\"form-control enhanced-field\" name=\"modules[\${moduleIndex}][features][]\" placeholder=\"e.g., Interactive practical exercises\" required style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; border-radius: 0.375rem 0 0 0.375rem;\">
                            <div class=\"input-group-append\">
                                <button type=\"button\" class=\"btn btn-success add-feature\" style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0 0.375rem 0.375rem 0;\">
                                    <i class=\"fas fa-plus\"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Hidden fields -->
                <input type=\"hidden\" name=\"modules[\${moduleIndex}][is_active]\" value=\"1\">
                <input type=\"hidden\" name=\"modules[\${moduleIndex}][sort_order]\" value=\"\${moduleIndex + 1}\">
            </div>
        `;
        \$('#modules-container').append(newModule);
        moduleIndex++;
        updateModuleNumbers();
    });

    // Remove module functionality
    \$(document).on('click', '.remove-module', function() {
        var moduleCount = \$('.module-item').length;
        if (moduleCount <= 1) {
            \$('#moduleValidationModal').modal('show');
            return;
        }
        \$(this).closest('.module-item').remove();
        updateModuleNumbers();
    });

    function updateModuleNumbers() {
        \$('.module-item').each(function(index) {
            \$(this).find('.module-number').text(index + 1);
        });
    }

    // Module outcome and feature management
    \$(document).on('click', '.add-outcome', function() {
        var container = \$(this).closest('.learning-outcomes-container');
        var moduleIndex = container.closest('.module-item').index();
        var newOutcome = `
            <div class=\"input-group mb-2\">
                <input type=\"text\" class=\"form-control enhanced-field\" name=\"modules[\${moduleIndex}][learning_outcomes][]\" placeholder=\"Enter learning outcome...\" required style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; border-radius: 0.375rem 0 0 0.375rem;\">
                <div class=\"input-group-append\">
                    <button type=\"button\" class=\"btn btn-danger remove-outcome\" style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0;\">
                        <i class=\"fas fa-minus\"></i>
                    </button>
                    <button type=\"button\" class=\"btn btn-success add-outcome\" style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0 0.375rem 0.375rem 0;\">
                        <i class=\"fas fa-plus\"></i>
                    </button>
                </div>
            </div>
        `;
        container.append(newOutcome);
    });

    \$(document).on('click', '.remove-outcome', function() {
        \$(this).closest('.input-group').remove();
    });

    \$(document).on('click', '.add-feature', function() {
        var container = \$(this).closest('.features-container');
        var moduleIndex = container.closest('.module-item').index();
        var newFeature = `
            <div class=\"input-group mb-2\">
                <input type=\"text\" class=\"form-control enhanced-field\" name=\"modules[\${moduleIndex}][features][]\" placeholder=\"Enter feature...\" required style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; border-radius: 0.375rem 0 0 0.375rem;\">
                <div class=\"input-group-append\">
                    <button type=\"button\" class=\"btn btn-danger remove-feature\" style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0;\">
                        <i class=\"fas fa-minus\"></i>
                    </button>
                    <button type=\"button\" class=\"btn btn-success add-feature\" style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0 0.375rem 0.375rem 0;\">
                        <i class=\"fas fa-plus\"></i>
                    </button>
                </div>
            </div>
        `;
        container.append(newFeature);
    });

    \$(document).on('click', '.remove-feature', function() {
        \$(this).closest('.input-group').remove();
    });

    // Enhanced category selection with search functionality
    const categorySelect = document.getElementById('category');
    if (categorySelect) {
        \$(categorySelect).select2({
            placeholder: 'Search and select a category...',
            allowClear: true,
            width: '100%',
            theme: 'bootstrap4'
        });
    }

    // Enhanced level selection with search functionality
    const levelSelect = document.getElementById('level');
    if (levelSelect) {
        \$(levelSelect).select2({
            placeholder: 'Search and select a level...',
            allowClear: true,
            width: '100%',
            theme: 'bootstrap4'
        });
    }
});
</script>

<!-- Include Select2 for enhanced dropdowns -->
<link href=\"https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css\" rel=\"stylesheet\" />
<link href=\"https://cdn.jsdelivr.net/npm/@ttskch/select2-bootstrap4-theme@x.x.x/dist/select2-bootstrap4.min.css\" rel=\"stylesheet\" />
<script src=\"https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js\"></script>

<style>
/* Select2 Custom Styling */
.select2-container--bootstrap4 .select2-selection--single {
    height: calc(1.6em + 1.25rem + 4px) !important;
    border: 2px solid #ced4da !important;
    border-radius: 8px !important;
    font-size: 1rem !important;
}

.select2-container--bootstrap4 .select2-selection--single .select2-selection__rendered {
    padding-left: 1rem !important;
    padding-top: 0.75rem !important;
    line-height: 1.5 !important;
}

.select2-container--bootstrap4 .select2-selection--single .select2-selection__arrow {
    height: calc(1.6em + 1.25rem + 2px) !important;
    right: 1rem !important;
}

.enhanced-dropdown option:first-child {
    color: #6c757d;
    font-style: italic;
    font-weight: 400;
}

.enhanced-dropdown option:not(:first-child) {
    color: #011a2d;
    font-weight: 500;
}
</style>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName(): string
    {
        return "admin/onsite_courses/create.html.twig";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable(): bool
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo(): array
    {
        return array (  635 => 433,  622 => 432,  546 => 366,  343 => 166,  316 => 142,  312 => 141,  308 => 140,  282 => 116,  269 => 114,  265 => 113,  193 => 44,  176 => 30,  158 => 14,  145 => 13,  131 => 9,  126 => 8,  113 => 7,  90 => 5,  67 => 3,  44 => 1,);
    }

    public function getSourceContext(): Source
    {
        return new Source("{% extends 'admin/base.html.twig' %}

{% block title %}Create Onsite Course - Capitol Academy Admin{% endblock %}

{% block page_title %}Create New Onsite Course{% endblock %}

{% block breadcrumbs %}
<li class=\"breadcrumb-item\"><a href=\"{{ path('admin_dashboard') }}\">Home</a></li>
<li class=\"breadcrumb-item\"><a href=\"{{ path('admin_onsite_courses') }}\">Onsite Courses</a></li>
<li class=\"breadcrumb-item active\">Create Onsite Course</li>
{% endblock %}

{% block content %}
<div class=\"container-fluid\">


    <!-- Integrated Header with Content -->
    <div class=\"card border-0 shadow-lg mb-4\">
        <div class=\"card-header\" style=\"background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%); color: white; padding: 1.5rem; border-radius: 15px 15px 0 0;\">
            <div class=\"row align-items-center\">
                <div class=\"col-md-6\">
                    <h2 class=\"card-title mb-0\" style=\"font-size: 1.8rem; font-weight: 600;\">
                        <i class=\"fas fa-graduation-cap mr-3\" style=\"font-size: 2rem;\"></i>
                        Create New Onsite Course
                    </h2>
                </div>
                <div class=\"col-md-6\">
                    <div class=\"d-flex justify-content-end align-items-center flex-wrap\">
                        <!-- Back to Onsite Courses Button -->
                        <a href=\"{{ path('admin_onsite_courses') }}\"
                           class=\"btn mb-2 mb-md-0\"
                           style=\"font-weight: 600; border-radius: 8px; padding: 0.75rem 1.5rem; background: white; color: #011a2d; border: 2px solid #011a2d; transition: all 0.3s ease;\"
                           onmouseover=\"this.style.background='#011a2d'; this.style.color='white';\"
                           onmouseout=\"this.style.background='white'; this.style.color='#011a2d';\">
                            <i class=\"fas fa-arrow-left me-2\"></i>
                            Back to Onsite Courses
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <form method=\"post\" id=\"course-form\" class=\"needs-validation\" enctype=\"multipart/form-data\" novalidate>
            <input type=\"hidden\" name=\"_token\" value=\"{{ csrf_token('course_create') }}\">
            <input type=\"hidden\" name=\"is_active\" value=\"1\">
            <div class=\"card-body\">
                    <!-- Single Column Layout -->
                    <div class=\"row\">
                        <div class=\"col-12\">
                            <!-- Course Code and Title Row -->
                            <div class=\"row\">
                                <!-- Course Code -->
                                <div class=\"col-md-6\">
                                    <div class=\"form-group\">
                                        <label for=\"code\" class=\"form-label\">
                                            <i class=\"fas fa-hashtag\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                            Onsite Course Code <span class=\"text-danger\">*</span>
                                        </label>
                                        <input type=\"text\"
                                               class=\"form-control enhanced-field\"
                                               id=\"code\"
                                               name=\"code\"
                                               placeholder=\"e.g., OSC101, TRAD200\"
                                               required
                                               maxlength=\"10\"
                                               pattern=\"[A-Za-z]{2,4}[0-9]{1,4}\"
                                               title=\"Format: 2-4 letters followed by 1-4 numbers (e.g., OSC101, TRAD200)\"
                                               style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; border-radius: 0.375rem;\">
                                        <div class=\"invalid-feedback\">
                                            Please provide a valid onsite course code (e.g., OSC101, TRAD200).
                                        </div>
                                    </div>
                                </div>

                                <!-- Course Title -->
                                <div class=\"col-md-6\">
                                    <div class=\"form-group\">
                                        <label for=\"title\" class=\"form-label\">
                                            <i class=\"fas fa-graduation-cap\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                            Onsite Course Title <span class=\"text-danger\">*</span>
                                        </label>
                                        <input type=\"text\"
                                               class=\"form-control enhanced-field\"
                                               id=\"title\"
                                               name=\"title\"
                                               placeholder=\"Enter comprehensive onsite course title\"
                                               required
                                               maxlength=\"255\"
                                               style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; border-radius: 0.375rem;\">
                                        <div class=\"invalid-feedback\">
                                            Please provide an onsite course title.
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Course Category and Level Row -->
                            <div class=\"row\">
                                <!-- Course Category -->
                                <div class=\"col-md-6\">
                                    <div class=\"form-group\">
                                        <label for=\"category\" class=\"form-label\">
                                            <i class=\"fas fa-folder text-primary mr-1\" aria-hidden=\"true\"></i>
                                            Category
                                        </label>
                                        <select class=\"form-select enhanced-dropdown\"
                                                id=\"category\"
                                                name=\"category\"
                                                aria-describedby=\"category_help category_error\"
                                                aria-label=\"Select a course category\"
                                                style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; background-color: #fff;\">
                                            <option value=\"\">Choose a category...</option>
                                            {% for category in categories %}
                                                <option value=\"{{ category.name }}\" {{ course.category == category.name ? 'selected' : '' }}>{{ category.name }}</option>
                                            {% endfor %}
                                        </select>
                                        <div id=\"category_help\" class=\"form-text text-muted\" style=\"display: none;\">
                                            Select the category that best describes this course. Use arrow keys to navigate options.
                                        </div>
                                        <div id=\"category_error\" class=\"invalid-feedback\" role=\"alert\" aria-live=\"polite\">
                                            Please select a category.
                                        </div>
                                    </div>
                                </div>

                                <!-- Course Level -->
                                <div class=\"col-md-6\">
                                    <div class=\"form-group\">
                                        <label for=\"level\" class=\"form-label\">
                                            <i class=\"fas fa-layer-group text-primary mr-1\" aria-hidden=\"true\"></i>
                                            Level
                                        </label>
                                        <select class=\"form-select enhanced-dropdown\"
                                                id=\"level\"
                                                name=\"level\"
                                                aria-describedby=\"level_help level_error\"
                                                aria-label=\"Select a course difficulty level\"
                                                style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; background-color: #fff;\">
                                            <option value=\"\">Choose a level...</option>
                                            <option value=\"Beginner\" {{ course.level == 'Beginner' ? 'selected' : '' }}>Beginner</option>
                                            <option value=\"Intermediate\" {{ course.level == 'Intermediate' ? 'selected' : '' }}>Intermediate</option>
                                            <option value=\"Advanced\" {{ course.level == 'Advanced' ? 'selected' : '' }}>Advanced</option>
                                        </select>
                                        <div id=\"level_help\" class=\"form-text text-muted\" style=\"display: none;\">
                                            Select the difficulty level that best matches this course content.
                                        </div>
                                        <div id=\"level_error\" class=\"invalid-feedback\" role=\"alert\" aria-live=\"polite\">
                                            Please select a level.
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Course Description -->
                            <div class=\"form-group\">
                                <label for=\"description\" class=\"form-label\">
                                    <i class=\"fas fa-align-left\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                    Onsite Course Description <span class=\"text-danger\">*</span>
                                </label>
                                <textarea class=\"form-control enhanced-field\"
                                          id=\"description\"
                                          name=\"description\"
                                          rows=\"8\"
                                          placeholder=\"Enter comprehensive onsite course description including objectives, target audience, and key topics...\"
                                          required
                                          style=\"min-height: 150px; font-size: 1rem; border: 2px solid #ced4da; border-radius: 0.375rem;\">{{ course.description }}</textarea>
                                <div class=\"invalid-feedback\">
                                    Please provide a comprehensive onsite course description.
                                </div>
                            </div>

                            <!-- Learning Outcomes -->
                            <div class=\"form-group\">
                                <label class=\"form-label\">
                                    <i class=\"fas fa-graduation-cap\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                    Learning Outcomes <span class=\"text-danger\">*</span>
                                </label>

                                <div id=\"learning-outcomes-container\">
                                    <div class=\"input-group mb-2 learning-outcome-item\">
                                        <input type=\"text\"
                                               class=\"form-control enhanced-field\"
                                               name=\"learning_outcomes[]\"
                                               placeholder=\"e.g., Master fundamental trading concepts and strategies\"
                                               required
                                               style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; border-radius: 0.375rem 0 0 0.375rem;\">
                                        <div class=\"input-group-append\">
                                            <button type=\"button\" class=\"btn add-learning-outcome\" style=\"background: #28a745; color: white; border: 2px solid #28a745; border-radius: 0 0.375rem 0.375rem 0; height: calc(1.6em + 1.25rem + 4px);\">
                                                <i class=\"fas fa-plus\"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                <div class=\"invalid-feedback\">
                                    Please provide at least one learning outcome.
                                </div>
                            </div>

                            <!-- Course Features -->
                            <div class=\"form-group\">
                                <label class=\"form-label\">
                                    <i class=\"fas fa-star\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                    Course Features <span class=\"text-danger\">*</span>
                                </label>

                                <div id=\"features-container\">
                                    <div class=\"input-group mb-2 feature-item\">
                                        <input type=\"text\"
                                               class=\"form-control enhanced-field\"
                                               name=\"features[]\"
                                               placeholder=\"e.g., Hands-on practical sessions, Expert instructor guidance, Course materials included\"
                                               required
                                               style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; border-radius: 0.375rem 0 0 0.375rem;\">
                                        <div class=\"input-group-append\">
                                            <button type=\"button\" class=\"btn add-feature\" style=\"background: #28a745; color: white; border: 2px solid #28a745; border-radius: 0 0.375rem 0.375rem 0; height: calc(1.6em + 1.25rem + 4px);\">
                                                <i class=\"fas fa-plus\"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                <div class=\"invalid-feedback\">
                                    Please provide at least one course feature.
                                </div>
                            </div>
                            <!-- Thumbnail Image (Full Width) -->
                            <div class=\"form-group\">
                                <label for=\"thumbnail_image\" class=\"form-label\">
                                    <i class=\"fas fa-image\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>Thumbnail Image <span class=\"text-danger\">*</span>
                                </label>
                                <input type=\"file\"
                                       class=\"form-control enhanced-file-field\"
                                       id=\"thumbnail_image\"
                                       name=\"thumbnail_image\"
                                       accept=\"image/jpeg,image/png,image/jpg\"
                                       required
                                       style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; background-color: #fff; border-radius: 0.375rem;\">

                                <div class=\"image-preview mt-3 text-center\" id=\"thumbnail-preview\" style=\"display: none;\">
                                    <div class=\"professional-image-container mx-auto\" style=\"width: 300px; height: 200px; border: 2px solid #1e3c72; border-radius: 8px; overflow: hidden; background: #f8f9fa; box-shadow: 0 2px 8px rgba(0,0,0,0.1);\">
                                        <img src=\"\" alt=\"Thumbnail Preview\" class=\"w-100 h-100\" style=\"object-fit: cover;\">
                                    </div>
                                </div>
                            </div>

                            <!-- Has Modules -->
                            <div class=\"form-group\" style=\"margin-bottom: 1.5rem;\">
                                <div class=\"form-check form-switch\" style=\"padding-left: 0; margin-left: 0;\">
                                    <input type=\"checkbox\"
                                           class=\"form-check-input\"
                                           id=\"has_modules\"
                                           name=\"has_modules\"
                                           value=\"1\"
                                           style=\"transform: scale(1.2); margin-left: 0;\">
                                    <label class=\"form-check-label\" for=\"has_modules\" style=\"margin-left: 2.5rem; padding-top: 0.125rem;\">
                                        <i class=\"fas fa-list\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                        <strong>Enable Course Modules</strong>
                                    </label>
                                </div>
                            </div>
                            <!-- Course Modules Management Section -->
                            <div id=\"modules-section\" class=\"form-group\" style=\"display: none;\">
                                <label class=\"form-label\">
                                    <i class=\"fas fa-puzzle-piece\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                    Course Modules
                                </label>

                                <div id=\"modules-container\">
                                    <div class=\"module-item\" style=\"border: 2px solid #007bff; border-radius: 12px; padding: 1.5rem; margin-bottom: 1.5rem; background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);\">
                                        <div class=\"module-header d-flex justify-content-between align-items-center mb-3\">
                                            <h6 class=\"mb-0\" style=\"color: #007bff; font-weight: 700;\">
                                                <i class=\"fas fa-cube\" style=\"margin-right: 0.5rem;\"></i>
                                                Module <span class=\"module-number\">1</span>
                                            </h6>
                                            <button type=\"button\" class=\"btn btn-sm btn-outline-danger remove-module\" style=\"border-radius: 50%; width: 35px; height: 35px;\">
                                                <i class=\"fas fa-times\"></i>
                                            </button>
                                        </div>

                                        <!-- Module Basic Info -->
                                        <div class=\"row\">
                                            <div class=\"col-md-6\">
                                                <div class=\"form-group\">
                                                    <label class=\"form-label\">
                                                        <i class=\"fas fa-hashtag\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                                        Module Code
                                                    </label>
                                                    <input type=\"text\" class=\"form-control enhanced-field module-code\" name=\"modules[0][code]\" placeholder=\"e.g., Module-1\" style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; border-radius: 0.375rem;\">
                                                </div>
                                            </div>
                                            <div class=\"col-md-6\">
                                                <div class=\"form-group\">
                                                    <label class=\"form-label\">
                                                        <i class=\"fas fa-tag\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                                        Module Title <span class=\"text-danger\">*</span>
                                                    </label>
                                                    <input type=\"text\" class=\"form-control enhanced-field module-title\" name=\"modules[0][title]\" placeholder=\"e.g., Introduction to Trading Fundamentals\" required style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; border-radius: 0.375rem;\">
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Module Description -->
                                        <div class=\"form-group\">
                                            <label class=\"form-label\">
                                                <i class=\"fas fa-align-left\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                                Module Description <span class=\"text-danger\">*</span>
                                            </label>
                                            <textarea class=\"form-control enhanced-field module-description\" name=\"modules[0][description]\" rows=\"6\" placeholder=\"Detailed description of what this module covers...\" required style=\"min-height: 100px;\"></textarea>
                                        </div>

                                        <!-- Module Learning Outcomes -->
                                        <div class=\"form-group\">
                                            <label class=\"form-label\">
                                                <i class=\"fas fa-graduation-cap\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                                Learning Outcomes <span class=\"text-danger\">*</span>
                                            </label>
                                            <div class=\"learning-outcomes-container\">
                                                <div class=\"input-group mb-2\">
                                                    <input type=\"text\" class=\"form-control enhanced-field\" name=\"modules[0][learning_outcomes][]\" placeholder=\"e.g., Master advanced chart analysis techniques\" required style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; border-radius: 0.375rem 0 0 0.375rem;\">
                                                    <div class=\"input-group-append\">
                                                        <button type=\"button\" class=\"btn btn-success add-outcome\" style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0 0.375rem 0.375rem 0;\">
                                                            <i class=\"fas fa-plus\"></i>
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Module Features -->
                                        <div class=\"form-group\">
                                            <label class=\"form-label\">
                                                <i class=\"fas fa-star\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                                Features <span class=\"text-danger\">*</span>
                                            </label>
                                            <div class=\"features-container\">
                                                <div class=\"input-group mb-2\">
                                                    <input type=\"text\" class=\"form-control enhanced-field\" name=\"modules[0][features][]\" placeholder=\"e.g., Interactive practical exercises\" required style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; border-radius: 0.375rem 0 0 0.375rem;\">
                                                    <div class=\"input-group-append\">
                                                        <button type=\"button\" class=\"btn btn-success add-feature\" style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0 0.375rem 0.375rem 0;\">
                                                            <i class=\"fas fa-plus\"></i>
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Hidden fields -->
                                        <input type=\"hidden\" name=\"modules[0][is_active]\" value=\"1\">
                                        <input type=\"hidden\" name=\"modules[0][sort_order]\" value=\"1\">
                                    </div>
                                </div>

                                <button type=\"button\" id=\"add-module\" class=\"btn btn-outline-primary\">
                                    <i class=\"fas fa-plus\" style=\"margin-right: 0.5rem;\"></i>
                                    Add Another Module
                                </button>
                            </div>

                        </div>

                        <!-- Action Buttons Section -->
                                    <div class=\"d-flex justify-content-between align-items-center flex-wrap gap-3\">
                                        <button type=\"submit\" class=\"btn btn-lg btn-success\" style=\"background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; border: none; font-weight: 600; border-radius: 8px; padding: 1rem 2.5rem; transition: all 0.3s ease; min-width: 200px;\">
                                            <i class=\"fas fa-save mr-2\"></i>
                                            Create Onsite Course
                                        </button>
                                        <a href=\"{{ path('admin_onsite_courses') }}\" class=\"btn btn-lg btn-secondary\" style=\"font-weight: 600; border-radius: 8px; padding: 1rem 2.5rem; min-width: 200px; background: #6c757d; border-color: #6c757d;\">
                                            <i class=\"fas fa-times mr-2\"></i>
                                            Cancel
                                        </a>
                                    </div>

                    </div>
                </div>

            </div>
        </form>
    </div>
</div>

<!-- Professional Module Removal Modal -->
<div class=\"modal fade\" id=\"courseValidationModal\" tabindex=\"-1\" aria-labelledby=\"courseValidationModalLabel\" aria-hidden=\"true\">
    <div class=\"modal-dialog modal-dialog-centered\">
        <div class=\"modal-content border-0 shadow-lg\">
            <div class=\"modal-header\" style=\"background: linear-gradient(135deg, #dc3545 0%, #c82333 100%); color: white; border: none;\">
                <h5 class=\"modal-title\" id=\"courseValidationModalLabel\">
                    <i class=\"fas fa-exclamation-triangle me-2\"></i>Cannot Remove Module
                </h5>
                <button type=\"button\" class=\"btn-close btn-close-white\" data-bs-dismiss=\"modal\" aria-label=\"Close\"></button>
            </div>
            <div class=\"modal-body text-center py-4\">
                <div class=\"mb-3\">
                    <i class=\"fas fa-info-circle text-danger\" style=\"font-size: 3rem;\"></i>
                </div>
                <h6 class=\"mt-3 mb-2\">Module Required</h6>
                <p class=\"text-muted mb-3\"><strong>Warning:</strong> A course must have at least one module.</p>
            </div>
            <div class=\"modal-footer border-0 justify-content-center\">
                <button type=\"button\" class=\"btn btn-primary\" data-bs-dismiss=\"modal\">
                    <i class=\"fas fa-check me-2\"></i>Understood
                </button>
            </div>
        </div>
    </div>
</div>
    <!-- Module Validation Modal -->
    <div class=\"modal fade\" id=\"moduleValidationModal\" tabindex=\"-1\" aria-labelledby=\"moduleValidationModalLabel\" aria-hidden=\"true\">
        <div class=\"modal-dialog modal-dialog-centered modal-sm\">
            <div class=\"modal-content\" style=\"border: none; border-radius: 8px; box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);\">
                <div class=\"modal-header\" style=\"background: linear-gradient(135deg, #a90418 0%, #8b0314 100%); color: white; border: none; padding: 1rem;\">
                    <h6 class=\"modal-title\" id=\"moduleValidationModalLabel\" style=\"font-weight: 600;\">
                        <i class=\"fas fa-exclamation-triangle me-2\"></i>Module Required
                    </h6>
                    <button type=\"button\" class=\"btn-close btn-close-white\" data-bs-dismiss=\"modal\" aria-label=\"Close\"></button>
                </div>
                <div class=\"modal-body\" style=\"padding: 1rem; text-align: center;\">
                    <p class=\"mb-3\" style=\"color: #a90418;\">
                        At least one module is required.
                    </p>
                    <small class=\"text-muted\">Please add a module or disable the \"Enable Course Modules\" option.</small>
                </div>
                <div class=\"modal-footer\" style=\"border: none; padding: 1rem; background: #f8f9fa;\">
                    <button type=\"button\" class=\"btn btn-primary btn-sm\" data-bs-dismiss=\"modal\" style=\"transition: all 0.3s ease;\">
                        <i class=\"fas fa-check me-1\"></i>OK
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block javascripts %}
<script>
\$(document).ready(function() {
    // Function to toggle module field validation based on \"Course Has Modules\" state
    function toggleModuleValidation() {
        var hasModules = \$('#has_modules').is(':checked');
        var moduleInputs = \$('#modules-section input[name*=\"modules\"], #modules-section textarea[name*=\"modules\"]');

        if (hasModules) {
            // Enable validation for module fields
            moduleInputs.each(function() {
                \$(this).attr('required', true);
            });
        } else {
            // Disable validation for module fields
            moduleInputs.each(function() {
                \$(this).removeAttr('required');
                // Clear any validation state
                \$(this).removeClass('is-invalid is-valid');
                \$(this).closest('.form-group').find('.invalid-feedback').hide();
            });
        }
    }

    // Bootstrap form validation
    (function() {
        'use strict';
        window.addEventListener('load', function() {
            var forms = document.getElementsByClassName('needs-validation');
            var validation = Array.prototype.filter.call(forms, function(form) {
                form.addEventListener('submit', function(event) {
                    console.log('Form submission attempted');

                    var submitBtn = form.querySelector('button[type=\"submit\"]');

                    // Update module validation based on toggle state before validation
                    toggleModuleValidation();

                    // Check if form is valid using native HTML5 validation
                    if (form.checkValidity() === false) {
                        console.log('Form validation failed');
                        event.preventDefault();
                        event.stopPropagation();

                        // Reset button state on validation failure
                        if (submitBtn) {
                            submitBtn.disabled = false;
                            submitBtn.innerHTML = '<i class=\"fas fa-save mr-2\"></i>Create Onsite Course';
                        }

                        // Show help text when validation fails
                        \$('.help-text').show();
                    } else {
                        console.log('Form validation passed, submitting...');
                        // Show loading state on successful validation
                        if (submitBtn) {
                            submitBtn.disabled = true;
                            submitBtn.innerHTML = '<i class=\"fas fa-spinner fa-spin mr-2\"></i>Creating Onsite Course...';
                        }

                        // Hide help text when form is valid
                        \$('.help-text').hide();
                    }

                    // Add validation classes for styling
                    form.classList.add('was-validated');
                }, false);
            });
        }, false);
    })();

    // Initialize module validation state on page load
    \$(document).ready(function() {
        toggleModuleValidation();
    });

    // Auto-generate course code suggestion
    \$('#title').on('input', function() {
        var title = \$(this).val();
        if (title.length > 0 && \$('#code').val() === '') {
            var words = title.split(' ');
            var suggestion = '';
            if (words.length >= 2) {
                suggestion = 'OSC' + words[0].substring(0, 1).toUpperCase() +
                           words[1].substring(0, 1).toUpperCase() +
                           '01';
            } else {
                suggestion = 'OSC' + title.substring(0, 3).toUpperCase() + '01';
            }
            \$('#code').attr('placeholder', 'Suggestion: ' + suggestion);
        }
    });

    // Price formatting
    \$('#price').on('blur', function() {
        var value = parseFloat(\$(this).val());
        if (!isNaN(value)) {
            \$(this).val(value.toFixed(2));
        }
    });

    // Form enhancement animations
    \$('.form-control').on('focus', function() {
        \$(this).closest('.form-group').addClass('focused');
    }).on('blur', function() {
        \$(this).closest('.form-group').removeClass('focused');
    });

    // Dynamic Learning Outcomes Management
    \$(document).on('click', '.add-learning-outcome', function() {
        var container = \$('#learning-outcomes-container');
        var newItem = `
            <div class=\"input-group mb-2 learning-outcome-item\">
                <input type=\"text\"
                       class=\"form-control enhanced-field\"
                       name=\"learning_outcomes[]\"
                       placeholder=\"Enter a learning outcome...\"
                       required
                       style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;\">
                <div class=\"input-group-append\">
                    <button type=\"button\" class=\"btn btn-danger remove-learning-outcome\">
                        <i class=\"fas fa-minus\"></i>
                    </button>
                    <button type=\"button\" class=\"btn btn-success add-learning-outcome\">
                        <i class=\"fas fa-plus\"></i>
                    </button>
                </div>
            </div>
        `;
        container.append(newItem);
    });

    \$(document).on('click', '.remove-learning-outcome', function() {
        \$(this).closest('.learning-outcome-item').remove();
    });

    // Dynamic Features Management
    \$(document).on('click', '.add-feature', function() {
        var container = \$('#features-container');
        var newItem = `
            <div class=\"input-group mb-2 feature-item\">
                <input type=\"text\"
                       class=\"form-control enhanced-field\"
                       name=\"features[]\"
                       placeholder=\"e.g., Hands-on practical sessions, Expert guidance...\"
                       style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;\">
                <div class=\"input-group-append\">
                    <button type=\"button\" class=\"btn btn-danger remove-feature\">
                        <i class=\"fas fa-minus\"></i>
                    </button>
                    <button type=\"button\" class=\"btn btn-success add-feature\">
                        <i class=\"fas fa-plus\"></i>
                    </button>
                </div>
            </div>
        `;
        container.append(newItem);
    });

    \$(document).on('click', '.remove-feature', function() {
        \$(this).closest('.feature-item').remove();
    });

    // Image Preview Functionality
    \$('#thumbnail_image').on('change', function() {
        previewImage(this, '#thumbnail-preview');
    });

    \$('#banner_image').on('change', function() {
        previewImage(this, '#banner-preview');
    });

    function previewImage(input, previewSelector) {
        if (input.files && input.files[0]) {
            var reader = new FileReader();
            reader.onload = function(e) {
                \$(previewSelector).show();
                \$(previewSelector + ' img').attr('src', e.target.result);
            };
            reader.readAsDataURL(input.files[0]);
        } else {
            \$(previewSelector).hide();
        }
    }

    // Module management
    var moduleIndex = 1;

    // Toggle module section visibility
    \$('#has_modules').on('change', function() {
        if (\$(this).is(':checked')) {
            \$('#modules-section').slideDown();
        } else {
            \$('#modules-section').slideUp();
        }
        toggleModuleValidation();
    });

    // Function to toggle module field validation based on \"Course Has Modules\" state
    function toggleModuleValidation() {
        var hasModules = \$('#has_modules').is(':checked');
        var moduleInputs = \$('#modules-section input[name*=\"modules\"], #modules-section textarea[name*=\"modules\"]');

        if (hasModules) {
            moduleInputs.attr('required', true);
        } else {
            moduleInputs.removeAttr('required');
        }
    }

    \$('#add-module').on('click', function() {
        var newModule = `
            <div class=\"module-item\" style=\"border: 2px solid #007bff; border-radius: 12px; padding: 1.5rem; margin-bottom: 1.5rem; background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);\">
                <div class=\"module-header d-flex justify-content-between align-items-center mb-3\">
                    <h6 class=\"mb-0\" style=\"color: #007bff; font-weight: 700;\">
                        <i class=\"fas fa-cube\" style=\"margin-right: 0.5rem;\"></i>
                        Module <span class=\"module-number\">\${moduleIndex + 1}</span>
                    </h6>
                    <button type=\"button\" class=\"btn btn-sm btn-outline-danger remove-module\" style=\"border-radius: 50%; width: 35px; height: 35px;\">
                        <i class=\"fas fa-times\"></i>
                    </button>
                </div>

                <!-- Module Basic Info -->
                <div class=\"row\">
                    <div class=\"col-md-6\">
                        <div class=\"form-group\">
                            <label class=\"form-label\">
                                <i class=\"fas fa-hashtag\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                Module Code
                            </label>
                            <input type=\"text\" class=\"form-control enhanced-field module-code\" name=\"modules[\${moduleIndex}][code]\" placeholder=\"e.g., Module-\${moduleIndex + 1}\" style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; border-radius: 0.375rem;\">
                        </div>
                    </div>
                    <div class=\"col-md-6\">
                        <div class=\"form-group\">
                            <label class=\"form-label\">
                                <i class=\"fas fa-tag\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                Module Title <span class=\"text-danger\">*</span>
                            </label>
                            <input type=\"text\" class=\"form-control enhanced-field module-title\" name=\"modules[\${moduleIndex}][title]\" placeholder=\"e.g., Advanced Trading Strategies\" required style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; border-radius: 0.375rem;\">
                        </div>
                    </div>
                </div>

                <!-- Module Description -->
                <div class=\"form-group\">
                    <label class=\"form-label\">
                        <i class=\"fas fa-align-left\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                        Module Description <span class=\"text-danger\">*</span>
                    </label>
                    <textarea class=\"form-control enhanced-field module-description\" name=\"modules[\${moduleIndex}][description]\" rows=\"6\" placeholder=\"Detailed description of what this module covers...\" required style=\"min-height: 150px;\"></textarea>
                </div>

                <!-- Module Learning Outcomes -->
                <div class=\"form-group\">
                    <label class=\"form-label\">
                        <i class=\"fas fa-graduation-cap\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                        Learning Outcomes <span class=\"text-danger\">*</span>
                    </label>
                    <div class=\"learning-outcomes-container\">
                        <div class=\"input-group mb-2\">
                            <input type=\"text\" class=\"form-control enhanced-field\" name=\"modules[\${moduleIndex}][learning_outcomes][]\" placeholder=\"e.g., Master advanced chart analysis techniques\" required style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; border-radius: 0.375rem 0 0 0.375rem;\">
                            <div class=\"input-group-append\">
                                <button type=\"button\" class=\"btn btn-success add-outcome\" style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0 0.375rem 0.375rem 0;\">
                                    <i class=\"fas fa-plus\"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Module Features -->
                <div class=\"form-group\">
                    <label class=\"form-label\">
                        <i class=\"fas fa-star\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                        Features <span class=\"text-danger\">*</span>
                    </label>
                    <div class=\"features-container\">
                        <div class=\"input-group mb-2\">
                            <input type=\"text\" class=\"form-control enhanced-field\" name=\"modules[\${moduleIndex}][features][]\" placeholder=\"e.g., Interactive practical exercises\" required style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; border-radius: 0.375rem 0 0 0.375rem;\">
                            <div class=\"input-group-append\">
                                <button type=\"button\" class=\"btn btn-success add-feature\" style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0 0.375rem 0.375rem 0;\">
                                    <i class=\"fas fa-plus\"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Hidden fields -->
                <input type=\"hidden\" name=\"modules[\${moduleIndex}][is_active]\" value=\"1\">
                <input type=\"hidden\" name=\"modules[\${moduleIndex}][sort_order]\" value=\"\${moduleIndex + 1}\">
            </div>
        `;
        \$('#modules-container').append(newModule);
        moduleIndex++;
        updateModuleNumbers();
    });

    // Remove module functionality
    \$(document).on('click', '.remove-module', function() {
        var moduleCount = \$('.module-item').length;
        if (moduleCount <= 1) {
            \$('#moduleValidationModal').modal('show');
            return;
        }
        \$(this).closest('.module-item').remove();
        updateModuleNumbers();
    });

    function updateModuleNumbers() {
        \$('.module-item').each(function(index) {
            \$(this).find('.module-number').text(index + 1);
        });
    }

    // Module outcome and feature management
    \$(document).on('click', '.add-outcome', function() {
        var container = \$(this).closest('.learning-outcomes-container');
        var moduleIndex = container.closest('.module-item').index();
        var newOutcome = `
            <div class=\"input-group mb-2\">
                <input type=\"text\" class=\"form-control enhanced-field\" name=\"modules[\${moduleIndex}][learning_outcomes][]\" placeholder=\"Enter learning outcome...\" required style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; border-radius: 0.375rem 0 0 0.375rem;\">
                <div class=\"input-group-append\">
                    <button type=\"button\" class=\"btn btn-danger remove-outcome\" style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0;\">
                        <i class=\"fas fa-minus\"></i>
                    </button>
                    <button type=\"button\" class=\"btn btn-success add-outcome\" style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0 0.375rem 0.375rem 0;\">
                        <i class=\"fas fa-plus\"></i>
                    </button>
                </div>
            </div>
        `;
        container.append(newOutcome);
    });

    \$(document).on('click', '.remove-outcome', function() {
        \$(this).closest('.input-group').remove();
    });

    \$(document).on('click', '.add-feature', function() {
        var container = \$(this).closest('.features-container');
        var moduleIndex = container.closest('.module-item').index();
        var newFeature = `
            <div class=\"input-group mb-2\">
                <input type=\"text\" class=\"form-control enhanced-field\" name=\"modules[\${moduleIndex}][features][]\" placeholder=\"Enter feature...\" required style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; border-radius: 0.375rem 0 0 0.375rem;\">
                <div class=\"input-group-append\">
                    <button type=\"button\" class=\"btn btn-danger remove-feature\" style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0;\">
                        <i class=\"fas fa-minus\"></i>
                    </button>
                    <button type=\"button\" class=\"btn btn-success add-feature\" style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0 0.375rem 0.375rem 0;\">
                        <i class=\"fas fa-plus\"></i>
                    </button>
                </div>
            </div>
        `;
        container.append(newFeature);
    });

    \$(document).on('click', '.remove-feature', function() {
        \$(this).closest('.input-group').remove();
    });

    // Enhanced category selection with search functionality
    const categorySelect = document.getElementById('category');
    if (categorySelect) {
        \$(categorySelect).select2({
            placeholder: 'Search and select a category...',
            allowClear: true,
            width: '100%',
            theme: 'bootstrap4'
        });
    }

    // Enhanced level selection with search functionality
    const levelSelect = document.getElementById('level');
    if (levelSelect) {
        \$(levelSelect).select2({
            placeholder: 'Search and select a level...',
            allowClear: true,
            width: '100%',
            theme: 'bootstrap4'
        });
    }
});
</script>

<!-- Include Select2 for enhanced dropdowns -->
<link href=\"https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css\" rel=\"stylesheet\" />
<link href=\"https://cdn.jsdelivr.net/npm/@ttskch/select2-bootstrap4-theme@x.x.x/dist/select2-bootstrap4.min.css\" rel=\"stylesheet\" />
<script src=\"https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js\"></script>

<style>
/* Select2 Custom Styling */
.select2-container--bootstrap4 .select2-selection--single {
    height: calc(1.6em + 1.25rem + 4px) !important;
    border: 2px solid #ced4da !important;
    border-radius: 8px !important;
    font-size: 1rem !important;
}

.select2-container--bootstrap4 .select2-selection--single .select2-selection__rendered {
    padding-left: 1rem !important;
    padding-top: 0.75rem !important;
    line-height: 1.5 !important;
}

.select2-container--bootstrap4 .select2-selection--single .select2-selection__arrow {
    height: calc(1.6em + 1.25rem + 2px) !important;
    right: 1rem !important;
}

.enhanced-dropdown option:first-child {
    color: #6c757d;
    font-style: italic;
    font-weight: 400;
}

.enhanced-dropdown option:not(:first-child) {
    color: #011a2d;
    font-weight: 500;
}
</style>
{% endblock %}
", "admin/onsite_courses/create.html.twig", "C:\\Users\\<USER>\\Desktop\\Capitol Academy\\Capitol-Academy\\templates\\admin\\onsite_courses\\create.html.twig");
    }
}
