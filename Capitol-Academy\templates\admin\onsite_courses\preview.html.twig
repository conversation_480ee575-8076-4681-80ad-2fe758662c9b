{% extends 'admin/base.html.twig' %}

{% block title %}Onsite Course Details - Capitol Academy Admin{% endblock %}

{% block page_title %}Onsite Course Details{% endblock %}

{% block breadcrumbs %}
<li class="breadcrumb-item"><a href="{{ path('admin_dashboard') }}">Home</a></li>
<li class="breadcrumb-item"><a href="{{ path('admin_onsite_courses') }}">Onsite Courses</a></li>
<li class="breadcrumb-item active">{{ course.title }}</li>
{% endblock %}

{% block content %}
<div class="container-fluid">


    <!-- Integrated Header with Content -->
    <div class="card border-0 shadow-lg mb-4">
        <div class="card-header" style="background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%); color: white; padding: 1.5rem; border-radius: 15px 15px 0 0;">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h2 class="card-title mb-0" style="font-size: 1.8rem; font-weight: 600;">
                        <i class="fas fa-graduation-cap mr-3" style="font-size: 2rem;"></i>
                        Onsite Course Details: {{ course.title }}
                    </h2>
                </div>
                <div class="col-md-6">
                    <div class="d-flex justify-content-end align-items-center flex-wrap">
                        <!-- Edit Onsite Course Button (Icon Only) -->
                        <a href="{{ path('admin_onsite_course_edit', {'code': course.code}) }}"
                           class="btn me-2 mb-2 mb-md-0"
                           style="border-radius: 50%; width: 45px; height: 45px; display: inline-flex; align-items: center; justify-content: center; background: white; border: 2px solid #011a2d; transition: all 0.3s ease;"
                           onmouseover="this.style.background='#011a2d'; this.querySelector('i').style.color='white';"
                           onmouseout="this.style.background='white'; this.querySelector('i').style.color='#011a2d';"
                           title="Edit Onsite Course">
                            <i class="fas fa-edit" style="color: #011a2d;"></i>
                        </a>

                        <!-- Print Onsite Course Button (Icon Only) -->
                        <a href="javascript:void(0)" onclick="window.print()"
                           class="btn me-2 mb-2 mb-md-0"
                           style="border-radius: 50%; width: 45px; height: 45px; display: inline-flex; align-items: center; justify-content: center; background: white; border: 2px solid #011a2d; transition: all 0.3s ease;"
                           onmouseover="this.style.background='#011a2d'; this.querySelector('i').style.color='white';"
                           onmouseout="this.style.background='white'; this.querySelector('i').style.color='#011a2d';"
                           title="Print Onsite Course Details">
                            <i class="fas fa-print" style="color: #011a2d;"></i>
                        </a>

                        <!-- Toggle Status Button -->
                        <button type="button"
                                class="btn me-2 mb-2 mb-md-0"
                                style="font-weight: 600; border-radius: 8px; padding: 0.75rem 1.5rem; background: {{ course.isActive ? '#6c757d' : '#28a745' }}; color: white; border: 2px solid {{ course.isActive ? '#6c757d' : '#28a745' }}; transition: all 0.3s ease;"
                                onclick="toggleOnsiteCourseStatus({{ course.id }}, '{{ course.title }}', {{ course.isActive ? 'true' : 'false' }})">
                            <i class="fas fa-{{ course.isActive ? 'pause' : 'play' }} me-2"></i>
                            {{ course.isActive ? 'Deactivate' : 'Activate' }}
                        </button>

                        <!-- Back to Onsite Courses Button -->
                        <a href="{{ path('admin_onsite_courses') }}"
                           class="btn mb-2 mb-md-0"
                           style="font-weight: 600; border-radius: 8px; padding: 0.75rem 1.5rem; background: white; color: #011a2d; border: 2px solid #011a2d; transition: all 0.3s ease;"
                           onmouseover="this.style.background='#011a2d'; this.style.color='white';"
                           onmouseout="this.style.background='white'; this.style.color='#011a2d';">
                            <i class="fas fa-arrow-left me-2"></i>
                            Back to Onsite Courses
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <div class="card-body">
            <div class="row">
                <div class="col-12">
                    <!-- Course Code and Title (same line) -->
                    <div class="row mb-4">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label class="form-label">
                                    <i class="fas fa-hashtag text-primary mr-1"></i>
                                    Course Code
                                </label>
                                <div class="enhanced-display-field" style="background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d;">
                                    {{ course.code }}
                                </div>
                            </div>
                        </div>
                        <div class="col-md-8">
                            <div class="form-group">
                                <label class="form-label">
                                    <i class="fas fa-graduation-cap text-primary mr-1"></i>
                                    Course Title
                                </label>
                                <div class="enhanced-display-field" style="background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d;">
                                    {{ course.title }}
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Description -->
                    <div class="form-group mb-4">
                        <label class="form-label">
                            <i class="fas fa-align-left text-primary mr-1"></i>
                            Description
                        </label>
                        <div class="enhanced-display-field" style="background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d; min-height: 80px;">
                            {{ course.description ?? 'No description provided' }}
                        </div>
                    </div>

                    <!-- Category and Level (same line) -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label">
                                    <i class="fas fa-folder text-primary mr-1"></i>
                                    Category
                                </label>
                                <div class="enhanced-display-field" style="background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d;">
                                    {{ course.category ?? 'Uncategorized' }}
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label">
                                    <i class="fas fa-layer-group text-primary mr-1"></i>
                                    Level
                                </label>
                                <div class="enhanced-display-field" style="background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d;">
                                    {{ course.level ?? 'Not specified' }}
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Learning Outcomes -->
                    {% if course.learningOutcomes and course.learningOutcomes|length > 0 %}
                    <div class="form-group mb-4">
                        <label class="form-label">
                            <i class="fas fa-bullseye text-primary mr-1"></i>
                            Learning Outcomes
                        </label>
                        <div class="enhanced-display-field" style="background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d;">
                            <ul class="mb-0" style="padding-left: 1.5rem;">
                                {% for outcome in course.learningOutcomes %}
                                    <li class="mb-2">
                                        <i class="fas fa-check-circle text-success me-2"></i>
                                        {{ outcome }}
                                    </li>
                                {% endfor %}
                            </ul>
                        </div>
                    </div>
                    {% endif %}

                    <!-- Features -->
                    {% if course.features and course.features|length > 0 %}
                    <div class="form-group mb-4">
                        <label class="form-label">
                            <i class="fas fa-star text-primary mr-1"></i>
                            Features
                        </label>
                        <div class="enhanced-display-field" style="background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d;">
                            <ul class="mb-0" style="padding-left: 1.5rem;">
                                {% for feature in course.features %}
                                    <li class="mb-2">
                                        <i class="fas fa-star text-warning me-2"></i>
                                        {{ feature }}
                                    </li>
                                {% endfor %}
                            </ul>
                        </div>
                    </div>
                    {% endif %}

                    <!-- Thumbnail -->
                    <div class="form-group mb-4">
                        <label class="form-label">
                            <i class="fas fa-image text-primary mr-1"></i>
                            Course Thumbnail
                        </label>
                        <div class="d-flex justify-content-center">
                            {% if course.thumbnailImage %}
                                <img src="{{ course.thumbnailUrl }}"
                                     alt="Course Thumbnail"
                                     class="img-fluid"
                                     style="width: 400px; height: 225px; object-fit: cover; border: 2px solid #1e3c72; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.15);">
                            {% else %}
                                <div class="d-flex align-items-center justify-content-center"
                                     style="width: 400px; height: 225px; border: 2px solid #ced4da; border-radius: 8px; background: #f8f9fa;">
                                    <div class="text-center text-muted">
                                        <i class="fas fa-image" style="font-size: 3rem; margin-bottom: 1rem;"></i>
                                        <p>No thumbnail available</p>
                                    </div>
                                </div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- Modules Information -->
                    {% if course.hasModules %}
                    <div class="form-group mb-4">
                        <label class="form-label">
                            <i class="fas fa-puzzle-piece text-primary mr-1"></i>
                            Course Modules
                        </label>
                        <div class="enhanced-display-field" style="background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d;">
                            {% if course.modules and course.modules|length > 0 %}
                                <div class="row">
                                    {% for module in course.modules %}
                                    <div class="col-12 mb-4">
                                        <div class="module-card" style="background: white; border: 2px solid #e9ecef; border-radius: 12px; padding: 1.5rem; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
                                            <!-- Module Header -->
                                            <div class="module-header mb-3" style="border-bottom: 2px solid #f8f9fa; padding-bottom: 1rem;">
                                                <h5 class="mb-2" style="color: #011a2d; font-weight: 600; font-size: 1.2rem;">
                                                    <i class="fas fa-cube me-2" style="color: #007bff;"></i>{{ module.title }}
                                                    <span class="badge bg-light text-dark ms-2" style="font-size: 0.7rem;">Module {{ loop.index }}</span>
                                                </h5>
                                                {% if module.description %}
                                                    <p class="mb-0 text-muted" style="font-size: 0.95rem; line-height: 1.5;">{{ module.description }}</p>
                                                {% endif %}
                                            </div>

                                            <!-- Module Content -->
                                            <div class="row">
                                                <!-- Learning Outcomes -->
                                                {% if module.learningOutcomes and module.learningOutcomes|length > 0 %}
                                                <div class="col-md-6 mb-3">
                                                    <h6 class="mb-3" style="color: #011a2d; font-weight: 600;">
                                                        <i class="fas fa-bullseye text-success me-2"></i>Learning Outcomes
                                                    </h6>
                                                    <ul class="mb-0" style="padding-left: 1rem; list-style: none;">
                                                        {% for outcome in module.learningOutcomes %}
                                                            <li class="mb-2" style="font-size: 0.9rem; line-height: 1.4;">
                                                                <i class="fas fa-check-circle text-success me-2" style="font-size: 0.8rem;"></i>
                                                                {{ outcome }}
                                                            </li>
                                                        {% endfor %}
                                                    </ul>
                                                </div>
                                                {% endif %}

                                                <!-- Features -->
                                                {% if module.features and module.features|length > 0 %}
                                                <div class="col-md-6 mb-3">
                                                    <h6 class="mb-3" style="color: #011a2d; font-weight: 600;">
                                                        <i class="fas fa-star text-warning me-2"></i>Features
                                                    </h6>
                                                    <ul class="mb-0" style="padding-left: 1rem; list-style: none;">
                                                        {% for feature in module.features %}
                                                            <li class="mb-2" style="font-size: 0.9rem; line-height: 1.4;">
                                                                <i class="fas fa-star text-warning me-2" style="font-size: 0.8rem;"></i>
                                                                {{ feature }}
                                                            </li>
                                                        {% endfor %}
                                                    </ul>
                                                </div>
                                                {% endif %}

                                                <!-- If no outcomes or features -->
                                                {% if (not module.learningOutcomes or module.learningOutcomes|length == 0) and (not module.features or module.features|length == 0) %}
                                                <div class="col-12">
                                                    <p class="mb-0 text-muted text-center" style="font-style: italic;">
                                                        <i class="fas fa-info-circle me-2"></i>No learning outcomes or features defined for this module yet.
                                                    </p>
                                                </div>
                                                {% endif %}
                                            </div>
                                        </div>
                                    </div>
                                    {% endfor %}
                                </div>
                            {% else %}
                                <p class="mb-0 text-muted">
                                    <i class="fas fa-info-circle me-2"></i>This course has modules enabled but no modules have been created yet.
                                </p>
                            {% endif %}
                        </div>
                    </div>
                    {% endif %}

                    <!-- Status and Creation Date -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label">
                                    <i class="fas fa-toggle-on text-primary mr-1"></i>
                                    Status
                                </label>
                                <div class="enhanced-display-field" style="background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d; min-height: 3.5rem; display: flex; align-items: center;">
                                    {% if course.isActive %}
                                        <span class="badge bg-success" style="font-size: 0.9rem; padding: 0.5rem 1rem;">
                                            <i class="fas fa-check-circle mr-1"></i>Active
                                        </span>
                                    {% else %}
                                        <span class="badge bg-secondary" style="font-size: 0.9rem; padding: 0.5rem 1rem;">
                                            <i class="fas fa-pause-circle mr-1"></i>Inactive
                                        </span>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label">
                                    <i class="fas fa-calendar-plus text-primary mr-1"></i>
                                    Creation Date
                                </label>
                                <div class="enhanced-display-field" style="background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d; min-height: 3.5rem; display: flex; align-items: center;">
                                    {{ course.createdAt|date('F j, Y \\a\\t g:i A') }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{% endblock %}
